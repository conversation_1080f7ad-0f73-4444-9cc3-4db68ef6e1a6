# eCommerce Project

A full-stack eCommerce platform built with Django REST Framework and React.

## Project Structure

- `backend/`: Django REST Framework backend
- `frontend/`: React frontend

## Quick Start

The easiest way to run the project is to use the provided script:

```bash
# Make the script executable
chmod +x run_project.sh

# Run the project
./run_project.sh
```

This will start both the backend and frontend servers in separate terminal tabs.

- Backend will be available at: http://localhost:8000
- Frontend will be available at: http://localhost:3000
- Admin interface will be available at: http://localhost:8000/admin

Default admin credentials:
- Username: admin
- Password: admin123

## Manual Setup

### Prerequisites

- Python 3.10+
- MySQL 8.0+
- Node.js 14+
- npm 6+
- pipenv

### Backend Setup

1. Install pipenv if not already installed:

```bash
pip install pipenv
```

2. Set up the virtual environment and install dependencies:

```bash
cd backend
pipenv install
```

3. Activate the virtual environment:

```bash
pipenv shell
```

4. Set up MySQL database:

```sql
CREATE DATABASE ecommerce_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ecommerce_user'@'localhost' IDENTIFIED BY 'ecommerce_password';
GRANT ALL PRIVILEGES ON ecommerce_db.* TO 'ecommerce_user'@'localhost';
FLUSH PRIVILEGES;
```

5. Run migrations and create sample data:

```bash
python manage.py makemigrations
python manage.py migrate
python setup.py
```

6. Run the development server:

```bash
python manage.py runserver
```

The API will be available at http://localhost:8000/api/

### Frontend Setup

1. Install dependencies:

```bash
cd frontend
npm install
```

2. Run the development server:

```bash
npm start
```

The frontend will be available at http://localhost:3000/

## API Endpoints

### Authentication

- `POST /api/auth/register/`: Register a new user
- `POST /api/auth/login/`: Login and get JWT token
- `POST /api/auth/refresh/`: Refresh JWT token

### Users

- `GET /api/users/`: List all users (admin only)
- `GET /api/users/{id}/`: Get user details
- `PUT /api/users/{id}/`: Update user profile

### Products

- `GET /api/products/`: List all products
- `GET /api/products/{id}/`: Get product details
- `POST /api/products/`: Create a new product (admin only)
- `PUT /api/products/{id}/`: Update product (admin only)
- `DELETE /api/products/{id}/`: Delete product (admin only)
- `GET /api/products/featured/`: Get featured products

### Categories

- `GET /api/categories/`: List all categories
- `GET /api/categories/{id}/`: Get category details
- `POST /api/categories/`: Create a new category (admin only)
- `PUT /api/categories/{id}/`: Update category (admin only)
- `DELETE /api/categories/{id}/`: Delete category (admin only)

### Cart

- `GET /api/carts/`: Get user's active cart
- `POST /api/carts/`: Create a new cart
- `PUT /api/carts/{id}/`: Update cart
- `DELETE /api/carts/{id}/`: Delete cart

### Cart Items

- `GET /api/carts/{cart_id}/items/`: Get cart items
- `POST /api/carts/{cart_id}/items/`: Add item to cart
- `PUT /api/carts/{cart_id}/items/{id}/`: Update cart item
- `DELETE /api/carts/{cart_id}/items/{id}/`: Remove item from cart

### Orders

- `GET /api/orders/`: List user's orders
- `GET /api/orders/{id}/`: Get order details
- `POST /api/orders/`: Create a new order
- `PUT /api/orders/{id}/`: Update order status (admin only)

### Order Lines

- `GET /api/orders/{order_id}/lines/`: Get order lines
- `POST /api/orders/{order_id}/lines/`: Add line to order (admin only)
- `PUT /api/orders/{order_id}/lines/{id}/`: Update order line (admin only)
- `DELETE /api/orders/{order_id}/lines/{id}/`: Remove line from order (admin only)

### Reviews

- `GET /api/reviews/`: List all reviews
- `GET /api/reviews/{id}/`: Get review details
- `POST /api/reviews/`: Create a new review
- `PUT /api/reviews/{id}/`: Update review (owner only)
- `DELETE /api/reviews/{id}/`: Delete review (owner or admin only)

### Offers

- `GET /api/offers/`: List all offers
- `GET /api/offers/{id}/`: Get offer details
- `POST /api/offers/`: Create a new offer (admin only)
- `PUT /api/offers/{id}/`: Update offer (admin only)
- `DELETE /api/offers/{id}/`: Delete offer (admin only)

### User Behavior Tracking

- `POST /api/track/`: Track user behavior

## Features

- User authentication and authorization with JWT
- Product catalog with categories and search functionality
- Shopping cart functionality
- Order management system
- Product reviews and ratings
- Special offers and discounts
- Admin dashboard for product moderation
- User behavior tracking for personalized recommendations
- Responsive design for mobile and desktop

## Database Schema

The project uses a comprehensive database schema with the following main entities:

- Users and Roles
- Products and Categories
- Carts and Cart Items
- Orders and Order Lines
- Reviews and Ratings
- Offers and Discounts
- User Behavior Tracking

## Technologies Used

### Backend
- Django 4.2
- Django REST Framework 3.14
- MySQL 8.0
- JWT Authentication
- Pipenv for dependency management

### Frontend
- React 18
- React Router 6
- Axios for API calls
- Tailwind CSS for styling
- React Hot Toast for notifications

## License

This project is licensed under the MIT License - see the LICENSE file for details.
