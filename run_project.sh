#!/bin/bash

echo "Starting eCommerce Project..."

# Start the Django backend server in the background
echo "Starting Django backend server..."
cd backend
./run_server.sh > backend_server.log 2>&1 &
BACKEND_PID=$!
cd ..

echo "Backend server started with PID: $BACKEND_PID"
echo "Backend logs are being written to backend/backend_server.log"

# Wait a bit for the backend to start
echo "Waiting for backend to initialize..."
sleep 5

# Start the React frontend server in the background
echo "Starting React frontend server..."
cd frontend
./run_server.sh > frontend_server.log 2>&1 &
FRONTEND_PID=$!
cd ..

echo "Frontend server started with PID: $FRONTEND_PID"
echo "Frontend logs are being written to frontend/frontend_server.log"

echo "Both servers are now running in the background."
echo "Backend will be available at: http://localhost:8000"
echo "Frontend will be available at: http://localhost:3000"
echo ""
echo "To view backend logs: tail -f backend/backend_server.log"
echo "To view frontend logs: tail -f frontend/frontend_server.log"
echo ""
echo "To stop the servers, run: kill $BACKEND_PID $FRONTEND_PID"
echo "Or press Ctrl+C to stop this script and then run: pkill -f 'python manage.py runserver' && pkill -f 'npm start'"
