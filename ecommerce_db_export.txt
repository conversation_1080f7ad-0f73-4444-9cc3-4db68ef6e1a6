# eCommerce Database Export
# Date: 2025-05-12 14:00:02
# Database: ecommerce_db

## Table: api_cart

### Structure:
- id: char(32) (Primary Key), NOT NULL
- status: varchar(20), NOT NULL
- created_at: datetime(6), NOT NULL
- updated_at: datetime(6), NOT NULL
- created_by_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_cartitem

### Structure:
- id: char(32) (Primary Key), NOT NULL
- price: decimal(10,2), NOT NULL
- quantity: int, NOT NULL
- created_at: datetime(6), NOT NULL
- cart_id: char(32) (Index), NOT NULL
- product_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_category

### Structure:
- id: char(32) (Primary Key), NOT NULL
- slug: varchar(50) (Unique), NOT NULL
- name: varchar(255), NOT NULL
- description: longtext
- tags: json, NOT NULL
- created_at: datetime(6), NOT NULL
- updated_at: datetime(6), NOT NULL
- parent_category_id: char(32) (Index)

### Data (5 rows):
Row 1:
{
  "id": "01cf69640bb54942ac1db2e02a6c7e08",
  "slug": "books",
  "name": "Books",
  "description": "Books and publications",
  "tags": "[\"books\"]",
  "created_at": "2025-05-01T15:02:28.775443",
  "updated_at": "2025-05-01T15:02:28.775525",
  "parent_category_id": null
}

Row 2:
{
  "id": "740c4719840b47549e627c252de20c28",
  "slug": "sports-outdoors",
  "name": "Sports & Outdoors",
  "description": "Sports equipment and outdoor gear",
  "tags": "[\"sports & outdoors\"]",
  "created_at": "2025-05-01T15:02:28.802960",
  "updated_at": "2025-05-01T15:02:28.803047",
  "parent_category_id": null
}

Row 3:
{
  "id": "8f7318420b2c4c6499a467ebfb1824f0",
  "slug": "electronics",
  "name": "Electronics",
  "description": "Electronic devices and gadgets",
  "tags": "[\"electronics\"]",
  "created_at": "2025-05-01T15:02:28.751735",
  "updated_at": "2025-05-01T15:02:28.751812",
  "parent_category_id": null
}

Row 4:
{
  "id": "abf3c19a336348399e9d014b1d4e1855",
  "slug": "clothing",
  "name": "Clothing",
  "description": "Apparel and fashion items",
  "tags": "[\"clothing\"]",
  "created_at": "2025-05-01T15:02:28.762835",
  "updated_at": "2025-05-01T15:02:28.762879",
  "parent_category_id": null
}

Row 5:
{
  "id": "fd677745a2034742b6ba98ce23601989",
  "slug": "home-kitchen",
  "name": "Home & Kitchen",
  "description": "Home and kitchen appliances",
  "tags": "[\"home & kitchen\"]",
  "created_at": "2025-05-01T15:02:28.791235",
  "updated_at": "2025-05-01T15:02:28.791283",
  "parent_category_id": null
}

--------------------------------------------------------------------------------

## Table: api_credential

### Structure:
- id: char(32) (Primary Key), NOT NULL
- provider_id: varchar(255), NOT NULL
- provider_key: char(32), NOT NULL
- password_hash: varchar(255)
- password_salt: varchar(255)
- created_at: datetime(6), NOT NULL
- user_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_offer

### Structure:
- id: char(32) (Primary Key), NOT NULL
- name: varchar(255), NOT NULL
- offer_type: varchar(20), NOT NULL
- discount_value: decimal(10,2), NOT NULL
- start_date: datetime(6), NOT NULL
- end_date: datetime(6), NOT NULL
- is_festival: tinyint(1), NOT NULL
- is_active: tinyint(1), NOT NULL
- product_id: char(32) (Index), NOT NULL

### Data (2 rows):
Row 1:
{
  "id": "22d139dd4cb74cae9731893113a194a3",
  "name": "Special Discount on Laptop Pro",
  "offer_type": "percentage",
  "discount_value": 15.0,
  "start_date": "2023-01-01T00:00:00",
  "end_date": "2023-12-31T23:59:59",
  "is_festival": 0,
  "is_active": 1,
  "product_id": "375da095b4734112b23ad6c124a4cebe"
}

Row 2:
{
  "id": "d6fa4ea849b8450689ed87ef362d79bb",
  "name": "Special Discount on Casual T-Shirt",
  "offer_type": "percentage",
  "discount_value": 15.0,
  "start_date": "2023-01-01T00:00:00",
  "end_date": "2023-12-31T23:59:59",
  "is_festival": 0,
  "is_active": 1,
  "product_id": "05e5e51455754a0390630cf388d4b4b9"
}

--------------------------------------------------------------------------------

## Table: api_order

### Structure:
- id: char(32) (Primary Key), NOT NULL
- total_price: decimal(10,2), NOT NULL
- payment_status: varchar(20), NOT NULL
- delivery_status: varchar(20), NOT NULL
- created_at: datetime(6), NOT NULL
- user_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_orderline

### Structure:
- id: char(32) (Primary Key), NOT NULL
- price: decimal(10,2), NOT NULL
- quantity: int, NOT NULL
- order_id: char(32) (Index), NOT NULL
- product_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_personalizedrecommendation

### Structure:
- id: char(32) (Primary Key), NOT NULL
- recommendation_score: double, NOT NULL
- created_at: datetime(6), NOT NULL
- product_id: char(32) (Index), NOT NULL
- user_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_product

### Structure:
- id: char(32) (Primary Key), NOT NULL
- title: varchar(255), NOT NULL
- slug: varchar(50) (Unique), NOT NULL
- summary: varchar(255)
- description: longtext
- picture: varchar(100)
- price: decimal(10,2), NOT NULL
- discount_type: varchar(20)
- discount_value: decimal(10,2), NOT NULL
- tags: json, NOT NULL
- stock_quantity: int, NOT NULL
- is_active: tinyint(1), NOT NULL
- created_at: datetime(6), NOT NULL
- updated_at: datetime(6), NOT NULL
- category_id: char(32) (Index), NOT NULL

### Data (4 rows):
Row 1:
{
  "id": "05e5e51455754a0390630cf388d4b4b9",
  "title": "Casual T-Shirt",
  "slug": "casual-t-shirt",
  "summary": "Comfortable cotton t-shirt",
  "description": "Soft, breathable cotton t-shirt perfect for everyday wear.",
  "picture": "",
  "price": 19.99,
  "discount_type": null,
  "discount_value": 0.0,
  "tags": "[\"t-shirt\", \"clothing\", \"casual\"]",
  "stock_quantity": 100,
  "is_active": 1,
  "created_at": "2025-05-01T15:02:28.855727",
  "updated_at": "2025-05-01T15:02:28.855787",
  "category_id": "abf3c19a336348399e9d014b1d4e1855"
}

Row 2:
{
  "id": "369e149ae2004d9a87e9b0dc0edd67d5",
  "title": "Python Programming",
  "slug": "python-programming",
  "summary": "Comprehensive guide to Python programming",
  "description": "Learn Python programming from basics to advanced concepts with practical examples.",
  "picture": "",
  "price": 39.99,
  "discount_type": null,
  "discount_value": 0.0,
  "tags": "[\"python\", \"programming\", \"book\"]",
  "stock_quantity": 75,
  "is_active": 1,
  "created_at": "2025-05-01T15:02:28.876484",
  "updated_at": "2025-05-01T15:02:28.876557",
  "category_id": "01cf69640bb54942ac1db2e02a6c7e08"
}

Row 3:
{
  "id": "375da095b4734112b23ad6c124a4cebe",
  "title": "Laptop Pro",
  "slug": "laptop-pro",
  "summary": "Professional laptop for work and entertainment",
  "description": "High-performance laptop with a stunning display, fast SSD storage, and powerful processor.",
  "picture": "",
  "price": 1299.99,
  "discount_type": null,
  "discount_value": 0.0,
  "tags": "[\"laptop\", \"electronics\", \"computer\"]",
  "stock_quantity": 30,
  "is_active": 1,
  "created_at": "2025-05-01T15:02:28.841675",
  "updated_at": "2025-05-01T15:02:28.841799",
  "category_id": "8f7318420b2c4c6499a467ebfb1824f0"
}

Row 4:
{
  "id": "7b4bf0b7fc4b4ed8becf3faedac33de2",
  "title": "Smartphone X",
  "slug": "smartphone-x",
  "summary": "Latest smartphone with advanced features",
  "description": "A powerful smartphone with a high-resolution camera, fast processor, and long battery life.",
  "picture": "",
  "price": 699.99,
  "discount_type": null,
  "discount_value": 0.0,
  "tags": "[\"smartphone\", \"electronics\", \"mobile\"]",
  "stock_quantity": 50,
  "is_active": 1,
  "created_at": "2025-05-01T15:02:28.824407",
  "updated_at": "2025-05-01T15:02:28.824483",
  "category_id": "8f7318420b2c4c6499a467ebfb1824f0"
}

--------------------------------------------------------------------------------

## Table: api_productmoderationlog

### Structure:
- id: char(32) (Primary Key), NOT NULL
- action: varchar(100), NOT NULL
- comments: longtext
- timestamp: datetime(6), NOT NULL
- moderator_id: char(32) (Index), NOT NULL
- product_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_review

### Structure:
- id: char(32) (Primary Key), NOT NULL
- rating: decimal(3,1), NOT NULL
- comment: longtext
- created_at: datetime(6), NOT NULL
- product_id: char(32) (Index), NOT NULL
- user_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_role

### Structure:
- id: char(32) (Primary Key), NOT NULL
- name: varchar(100), NOT NULL
- description: longtext

### Data (3 rows):
Row 1:
{
  "id": "934a1c28e54341f7b6ee7f5cd1c5520a",
  "name": "Admin",
  "description": "Admin role"
}

Row 2:
{
  "id": "984dba2c11ec4f0b9f6ba365958938b1",
  "name": "Customer",
  "description": "Customer role"
}

Row 3:
{
  "id": "bf8b121eab514024a623e7e6b41c9414",
  "name": "Moderator",
  "description": "Moderator role"
}

--------------------------------------------------------------------------------

## Table: api_socialprofile

### Structure:
- id: bigint (Primary Key), NOT NULL
- platform: varchar(100), NOT NULL
- platform_user: varchar(255), NOT NULL
- created_at: datetime(6), NOT NULL
- user_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_user

### Structure:
- password: varchar(128), NOT NULL
- is_superuser: tinyint(1), NOT NULL
- username: varchar(150) (Unique), NOT NULL
- first_name: varchar(150), NOT NULL
- last_name: varchar(150), NOT NULL
- is_staff: tinyint(1), NOT NULL
- is_active: tinyint(1), NOT NULL
- date_joined: datetime(6), NOT NULL
- id: char(32) (Primary Key), NOT NULL
- slug: varchar(50) (Unique), NOT NULL
- email: varchar(254) (Unique), NOT NULL
- phone: varchar(20)
- avatar: varchar(100)
- locale: varchar(10), NOT NULL
- bio: longtext
- company: varchar(255)
- email_validated: tinyint(1), NOT NULL
- phone_validated: tinyint(1), NOT NULL
- last_login: datetime(6)
- created_at: datetime(6), NOT NULL
- updated_at: datetime(6), NOT NULL

### Data (1 rows):
Row 1:
{
  "password": "pbkdf2_sha256$600000$hLSa25HOeFL9fvxB9k0Vfw$74dws/H1fCoWHayERxoR9lYNmjI9VoP+nDaL7CRxKMQ=",
  "is_superuser": 1,
  "username": "admin",
  "first_name": "",
  "last_name": "",
  "is_staff": 1,
  "is_active": 1,
  "date_joined": "2025-05-01T15:02:27.510492",
  "id": "a02df74fb39045d1b040bc11649363f9",
  "slug": "admin",
  "email": "<EMAIL>",
  "phone": null,
  "avatar": "",
  "locale": "en-US",
  "bio": null,
  "company": null,
  "email_validated": 0,
  "phone_validated": 0,
  "last_login": null,
  "created_at": "2025-05-01T15:02:28.692804",
  "updated_at": "2025-05-01T15:02:28.692891"
}

--------------------------------------------------------------------------------

## Table: api_user_groups

### Structure:
- id: bigint (Primary Key), NOT NULL
- user_id: char(32) (Index), NOT NULL
- group_id: int (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_user_user_permissions

### Structure:
- id: bigint (Primary Key), NOT NULL
- user_id: char(32) (Index), NOT NULL
- permission_id: int (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_userbehavior

### Structure:
- id: char(32) (Primary Key), NOT NULL
- action: varchar(20), NOT NULL
- metadata: json, NOT NULL
- action_time: datetime(6), NOT NULL
- product_id: char(32) (Index), NOT NULL
- user_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: api_userrole

### Structure:
- id: char(32) (Primary Key), NOT NULL
- assigned_at: datetime(6), NOT NULL
- role_id: char(32) (Index), NOT NULL
- user_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: auth_group

### Structure:
- id: int (Primary Key), NOT NULL
- name: varchar(150) (Unique), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: auth_group_permissions

### Structure:
- id: bigint (Primary Key), NOT NULL
- group_id: int (Index), NOT NULL
- permission_id: int (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: auth_permission

### Structure:
- id: int (Primary Key), NOT NULL
- name: varchar(255), NOT NULL
- content_type_id: int (Index), NOT NULL
- codename: varchar(100), NOT NULL

### Data (84 rows):
Row 1:
{
  "id": 1,
  "name": "Can add log entry",
  "content_type_id": 1,
  "codename": "add_logentry"
}

Row 2:
{
  "id": 2,
  "name": "Can change log entry",
  "content_type_id": 1,
  "codename": "change_logentry"
}

Row 3:
{
  "id": 3,
  "name": "Can delete log entry",
  "content_type_id": 1,
  "codename": "delete_logentry"
}

Row 4:
{
  "id": 4,
  "name": "Can view log entry",
  "content_type_id": 1,
  "codename": "view_logentry"
}

Row 5:
{
  "id": 5,
  "name": "Can add permission",
  "content_type_id": 2,
  "codename": "add_permission"
}

Row 6:
{
  "id": 6,
  "name": "Can change permission",
  "content_type_id": 2,
  "codename": "change_permission"
}

Row 7:
{
  "id": 7,
  "name": "Can delete permission",
  "content_type_id": 2,
  "codename": "delete_permission"
}

Row 8:
{
  "id": 8,
  "name": "Can view permission",
  "content_type_id": 2,
  "codename": "view_permission"
}

Row 9:
{
  "id": 9,
  "name": "Can add group",
  "content_type_id": 3,
  "codename": "add_group"
}

Row 10:
{
  "id": 10,
  "name": "Can change group",
  "content_type_id": 3,
  "codename": "change_group"
}

Row 11:
{
  "id": 11,
  "name": "Can delete group",
  "content_type_id": 3,
  "codename": "delete_group"
}

Row 12:
{
  "id": 12,
  "name": "Can view group",
  "content_type_id": 3,
  "codename": "view_group"
}

Row 13:
{
  "id": 13,
  "name": "Can add content type",
  "content_type_id": 4,
  "codename": "add_contenttype"
}

Row 14:
{
  "id": 14,
  "name": "Can change content type",
  "content_type_id": 4,
  "codename": "change_contenttype"
}

Row 15:
{
  "id": 15,
  "name": "Can delete content type",
  "content_type_id": 4,
  "codename": "delete_contenttype"
}

Row 16:
{
  "id": 16,
  "name": "Can view content type",
  "content_type_id": 4,
  "codename": "view_contenttype"
}

Row 17:
{
  "id": 17,
  "name": "Can add session",
  "content_type_id": 5,
  "codename": "add_session"
}

Row 18:
{
  "id": 18,
  "name": "Can change session",
  "content_type_id": 5,
  "codename": "change_session"
}

Row 19:
{
  "id": 19,
  "name": "Can delete session",
  "content_type_id": 5,
  "codename": "delete_session"
}

Row 20:
{
  "id": 20,
  "name": "Can view session",
  "content_type_id": 5,
  "codename": "view_session"
}

Row 21:
{
  "id": 21,
  "name": "Can add user",
  "content_type_id": 6,
  "codename": "add_user"
}

Row 22:
{
  "id": 22,
  "name": "Can change user",
  "content_type_id": 6,
  "codename": "change_user"
}

Row 23:
{
  "id": 23,
  "name": "Can delete user",
  "content_type_id": 6,
  "codename": "delete_user"
}

Row 24:
{
  "id": 24,
  "name": "Can view user",
  "content_type_id": 6,
  "codename": "view_user"
}

Row 25:
{
  "id": 25,
  "name": "Can add cart",
  "content_type_id": 7,
  "codename": "add_cart"
}

Row 26:
{
  "id": 26,
  "name": "Can change cart",
  "content_type_id": 7,
  "codename": "change_cart"
}

Row 27:
{
  "id": 27,
  "name": "Can delete cart",
  "content_type_id": 7,
  "codename": "delete_cart"
}

Row 28:
{
  "id": 28,
  "name": "Can view cart",
  "content_type_id": 7,
  "codename": "view_cart"
}

Row 29:
{
  "id": 29,
  "name": "Can add category",
  "content_type_id": 8,
  "codename": "add_category"
}

Row 30:
{
  "id": 30,
  "name": "Can change category",
  "content_type_id": 8,
  "codename": "change_category"
}

Row 31:
{
  "id": 31,
  "name": "Can delete category",
  "content_type_id": 8,
  "codename": "delete_category"
}

Row 32:
{
  "id": 32,
  "name": "Can view category",
  "content_type_id": 8,
  "codename": "view_category"
}

Row 33:
{
  "id": 33,
  "name": "Can add order",
  "content_type_id": 9,
  "codename": "add_order"
}

Row 34:
{
  "id": 34,
  "name": "Can change order",
  "content_type_id": 9,
  "codename": "change_order"
}

Row 35:
{
  "id": 35,
  "name": "Can delete order",
  "content_type_id": 9,
  "codename": "delete_order"
}

Row 36:
{
  "id": 36,
  "name": "Can view order",
  "content_type_id": 9,
  "codename": "view_order"
}

Row 37:
{
  "id": 37,
  "name": "Can add product",
  "content_type_id": 10,
  "codename": "add_product"
}

Row 38:
{
  "id": 38,
  "name": "Can change product",
  "content_type_id": 10,
  "codename": "change_product"
}

Row 39:
{
  "id": 39,
  "name": "Can delete product",
  "content_type_id": 10,
  "codename": "delete_product"
}

Row 40:
{
  "id": 40,
  "name": "Can view product",
  "content_type_id": 10,
  "codename": "view_product"
}

Row 41:
{
  "id": 41,
  "name": "Can add role",
  "content_type_id": 11,
  "codename": "add_role"
}

Row 42:
{
  "id": 42,
  "name": "Can change role",
  "content_type_id": 11,
  "codename": "change_role"
}

Row 43:
{
  "id": 43,
  "name": "Can delete role",
  "content_type_id": 11,
  "codename": "delete_role"
}

Row 44:
{
  "id": 44,
  "name": "Can view role",
  "content_type_id": 11,
  "codename": "view_role"
}

Row 45:
{
  "id": 45,
  "name": "Can add user behavior",
  "content_type_id": 12,
  "codename": "add_userbehavior"
}

Row 46:
{
  "id": 46,
  "name": "Can change user behavior",
  "content_type_id": 12,
  "codename": "change_userbehavior"
}

Row 47:
{
  "id": 47,
  "name": "Can delete user behavior",
  "content_type_id": 12,
  "codename": "delete_userbehavior"
}

Row 48:
{
  "id": 48,
  "name": "Can view user behavior",
  "content_type_id": 12,
  "codename": "view_userbehavior"
}

Row 49:
{
  "id": 49,
  "name": "Can add product moderation log",
  "content_type_id": 13,
  "codename": "add_productmoderationlog"
}

Row 50:
{
  "id": 50,
  "name": "Can change product moderation log",
  "content_type_id": 13,
  "codename": "change_productmoderationlog"
}

Row 51:
{
  "id": 51,
  "name": "Can delete product moderation log",
  "content_type_id": 13,
  "codename": "delete_productmoderationlog"
}

Row 52:
{
  "id": 52,
  "name": "Can view product moderation log",
  "content_type_id": 13,
  "codename": "view_productmoderationlog"
}

Row 53:
{
  "id": 53,
  "name": "Can add order line",
  "content_type_id": 14,
  "codename": "add_orderline"
}

Row 54:
{
  "id": 54,
  "name": "Can change order line",
  "content_type_id": 14,
  "codename": "change_orderline"
}

Row 55:
{
  "id": 55,
  "name": "Can delete order line",
  "content_type_id": 14,
  "codename": "delete_orderline"
}

Row 56:
{
  "id": 56,
  "name": "Can view order line",
  "content_type_id": 14,
  "codename": "view_orderline"
}

Row 57:
{
  "id": 57,
  "name": "Can add offer",
  "content_type_id": 15,
  "codename": "add_offer"
}

Row 58:
{
  "id": 58,
  "name": "Can change offer",
  "content_type_id": 15,
  "codename": "change_offer"
}

Row 59:
{
  "id": 59,
  "name": "Can delete offer",
  "content_type_id": 15,
  "codename": "delete_offer"
}

Row 60:
{
  "id": 60,
  "name": "Can view offer",
  "content_type_id": 15,
  "codename": "view_offer"
}

Row 61:
{
  "id": 61,
  "name": "Can add credential",
  "content_type_id": 16,
  "codename": "add_credential"
}

Row 62:
{
  "id": 62,
  "name": "Can change credential",
  "content_type_id": 16,
  "codename": "change_credential"
}

Row 63:
{
  "id": 63,
  "name": "Can delete credential",
  "content_type_id": 16,
  "codename": "delete_credential"
}

Row 64:
{
  "id": 64,
  "name": "Can view credential",
  "content_type_id": 16,
  "codename": "view_credential"
}

Row 65:
{
  "id": 65,
  "name": "Can add cart item",
  "content_type_id": 17,
  "codename": "add_cartitem"
}

Row 66:
{
  "id": 66,
  "name": "Can change cart item",
  "content_type_id": 17,
  "codename": "change_cartitem"
}

Row 67:
{
  "id": 67,
  "name": "Can delete cart item",
  "content_type_id": 17,
  "codename": "delete_cartitem"
}

Row 68:
{
  "id": 68,
  "name": "Can view cart item",
  "content_type_id": 17,
  "codename": "view_cartitem"
}

Row 69:
{
  "id": 69,
  "name": "Can add user role",
  "content_type_id": 18,
  "codename": "add_userrole"
}

Row 70:
{
  "id": 70,
  "name": "Can change user role",
  "content_type_id": 18,
  "codename": "change_userrole"
}

Row 71:
{
  "id": 71,
  "name": "Can delete user role",
  "content_type_id": 18,
  "codename": "delete_userrole"
}

Row 72:
{
  "id": 72,
  "name": "Can view user role",
  "content_type_id": 18,
  "codename": "view_userrole"
}

Row 73:
{
  "id": 73,
  "name": "Can add social profile",
  "content_type_id": 19,
  "codename": "add_socialprofile"
}

Row 74:
{
  "id": 74,
  "name": "Can change social profile",
  "content_type_id": 19,
  "codename": "change_socialprofile"
}

Row 75:
{
  "id": 75,
  "name": "Can delete social profile",
  "content_type_id": 19,
  "codename": "delete_socialprofile"
}

Row 76:
{
  "id": 76,
  "name": "Can view social profile",
  "content_type_id": 19,
  "codename": "view_socialprofile"
}

Row 77:
{
  "id": 77,
  "name": "Can add review",
  "content_type_id": 20,
  "codename": "add_review"
}

Row 78:
{
  "id": 78,
  "name": "Can change review",
  "content_type_id": 20,
  "codename": "change_review"
}

Row 79:
{
  "id": 79,
  "name": "Can delete review",
  "content_type_id": 20,
  "codename": "delete_review"
}

Row 80:
{
  "id": 80,
  "name": "Can view review",
  "content_type_id": 20,
  "codename": "view_review"
}

Row 81:
{
  "id": 81,
  "name": "Can add personalized recommendation",
  "content_type_id": 21,
  "codename": "add_personalizedrecommendation"
}

Row 82:
{
  "id": 82,
  "name": "Can change personalized recommendation",
  "content_type_id": 21,
  "codename": "change_personalizedrecommendation"
}

Row 83:
{
  "id": 83,
  "name": "Can delete personalized recommendation",
  "content_type_id": 21,
  "codename": "delete_personalizedrecommendation"
}

Row 84:
{
  "id": 84,
  "name": "Can view personalized recommendation",
  "content_type_id": 21,
  "codename": "view_personalizedrecommendation"
}

--------------------------------------------------------------------------------

## Table: django_admin_log

### Structure:
- id: int (Primary Key), NOT NULL
- action_time: datetime(6), NOT NULL
- object_id: longtext
- object_repr: varchar(200), NOT NULL
- action_flag: smallint unsigned, NOT NULL
- change_message: longtext, NOT NULL
- content_type_id: int (Index)
- user_id: char(32) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

## Table: django_content_type

### Structure:
- id: int (Primary Key), NOT NULL
- app_label: varchar(100) (Index), NOT NULL
- model: varchar(100), NOT NULL

### Data (21 rows):
Row 1:
{
  "id": 1,
  "app_label": "admin",
  "model": "logentry"
}

Row 2:
{
  "id": 7,
  "app_label": "api",
  "model": "cart"
}

Row 3:
{
  "id": 17,
  "app_label": "api",
  "model": "cartitem"
}

Row 4:
{
  "id": 8,
  "app_label": "api",
  "model": "category"
}

Row 5:
{
  "id": 16,
  "app_label": "api",
  "model": "credential"
}

Row 6:
{
  "id": 15,
  "app_label": "api",
  "model": "offer"
}

Row 7:
{
  "id": 9,
  "app_label": "api",
  "model": "order"
}

Row 8:
{
  "id": 14,
  "app_label": "api",
  "model": "orderline"
}

Row 9:
{
  "id": 21,
  "app_label": "api",
  "model": "personalizedrecommendation"
}

Row 10:
{
  "id": 10,
  "app_label": "api",
  "model": "product"
}

Row 11:
{
  "id": 13,
  "app_label": "api",
  "model": "productmoderationlog"
}

Row 12:
{
  "id": 20,
  "app_label": "api",
  "model": "review"
}

Row 13:
{
  "id": 11,
  "app_label": "api",
  "model": "role"
}

Row 14:
{
  "id": 19,
  "app_label": "api",
  "model": "socialprofile"
}

Row 15:
{
  "id": 6,
  "app_label": "api",
  "model": "user"
}

Row 16:
{
  "id": 12,
  "app_label": "api",
  "model": "userbehavior"
}

Row 17:
{
  "id": 18,
  "app_label": "api",
  "model": "userrole"
}

Row 18:
{
  "id": 3,
  "app_label": "auth",
  "model": "group"
}

Row 19:
{
  "id": 2,
  "app_label": "auth",
  "model": "permission"
}

Row 20:
{
  "id": 4,
  "app_label": "contenttypes",
  "model": "contenttype"
}

Row 21:
{
  "id": 5,
  "app_label": "sessions",
  "model": "session"
}

--------------------------------------------------------------------------------

## Table: django_migrations

### Structure:
- id: bigint (Primary Key), NOT NULL
- app: varchar(255), NOT NULL
- name: varchar(255), NOT NULL
- applied: datetime(6), NOT NULL

### Data (19 rows):
Row 1:
{
  "id": 1,
  "app": "contenttypes",
  "name": "0001_initial",
  "applied": "2025-05-01T15:01:56.735994"
}

Row 2:
{
  "id": 2,
  "app": "contenttypes",
  "name": "0002_remove_content_type_name",
  "applied": "2025-05-01T15:01:57.071652"
}

Row 3:
{
  "id": 3,
  "app": "auth",
  "name": "0001_initial",
  "applied": "2025-05-01T15:01:57.970882"
}

Row 4:
{
  "id": 4,
  "app": "auth",
  "name": "0002_alter_permission_name_max_length",
  "applied": "2025-05-01T15:01:58.197592"
}

Row 5:
{
  "id": 5,
  "app": "auth",
  "name": "0003_alter_user_email_max_length",
  "applied": "2025-05-01T15:01:58.213490"
}

Row 6:
{
  "id": 6,
  "app": "auth",
  "name": "0004_alter_user_username_opts",
  "applied": "2025-05-01T15:01:58.234515"
}

Row 7:
{
  "id": 7,
  "app": "auth",
  "name": "0005_alter_user_last_login_null",
  "applied": "2025-05-01T15:01:58.256048"
}

Row 8:
{
  "id": 8,
  "app": "auth",
  "name": "0006_require_contenttypes_0002",
  "applied": "2025-05-01T15:01:58.268075"
}

Row 9:
{
  "id": 9,
  "app": "auth",
  "name": "0007_alter_validators_add_error_messages",
  "applied": "2025-05-01T15:01:58.287226"
}

Row 10:
{
  "id": 10,
  "app": "auth",
  "name": "0008_alter_user_username_max_length",
  "applied": "2025-05-01T15:01:58.307734"
}

Row 11:
{
  "id": 11,
  "app": "auth",
  "name": "0009_alter_user_last_name_max_length",
  "applied": "2025-05-01T15:01:58.329157"
}

Row 12:
{
  "id": 12,
  "app": "auth",
  "name": "0010_alter_group_name_max_length",
  "applied": "2025-05-01T15:01:58.372016"
}

Row 13:
{
  "id": 13,
  "app": "auth",
  "name": "0011_update_proxy_permissions",
  "applied": "2025-05-01T15:01:58.395384"
}

Row 14:
{
  "id": 14,
  "app": "auth",
  "name": "0012_alter_user_first_name_max_length",
  "applied": "2025-05-01T15:01:58.414249"
}

Row 15:
{
  "id": 15,
  "app": "api",
  "name": "0001_initial",
  "applied": "2025-05-01T15:02:06.856467"
}

Row 16:
{
  "id": 16,
  "app": "admin",
  "name": "0001_initial",
  "applied": "2025-05-01T15:02:07.402577"
}

Row 17:
{
  "id": 17,
  "app": "admin",
  "name": "0002_logentry_remove_auto_add",
  "applied": "2025-05-01T15:02:07.445153"
}

Row 18:
{
  "id": 18,
  "app": "admin",
  "name": "0003_logentry_add_action_flag_choices",
  "applied": "2025-05-01T15:02:07.540188"
}

Row 19:
{
  "id": 19,
  "app": "sessions",
  "name": "0001_initial",
  "applied": "2025-05-01T15:02:07.685441"
}

--------------------------------------------------------------------------------

## Table: django_session

### Structure:
- session_key: varchar(40) (Primary Key), NOT NULL
- session_data: longtext, NOT NULL
- expire_date: datetime(6) (Index), NOT NULL

### Data (0 rows):
No data

--------------------------------------------------------------------------------

