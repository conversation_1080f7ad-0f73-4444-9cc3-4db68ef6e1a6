Webpage,Route,Main Components,Child Components,APIs Used,Database Models,Description,Authentication Required
Home Page,/,HomePage.jsx,"Layout.jsx, Hero.jsx, FeaturedProducts.jsx, CategoryList.jsx, Offers.jsx, PersonalizedRecommendations.jsx","/api/home/<USER>/api/products/, /api/categories/, /api/offers/","Product, Category, Offer","Main landing page with featured products, categories, and special offers",No
Product Listing,/products,ProductListingPage.jsx,"Layout.jsx, FilterSideBar.jsx, ProductGrid.jsx, Pagination.jsx, SortDropdown.jsx","/api/products/, /api/categories/, /api/products?filter=, /api/products?sort=","Product, Category","Displays all products with filtering, sorting, and pagination options",No
Product Detail,/products/:productId,ProductDetailPage.jsx,"Layout.jsx, ProductGallery.jsx, ProductInfo.jsx, ProductDescription.jsx, QuantitySelector.jsx, Reviews.jsx, RelatedProducts.jsx","/api/products/:id, /api/products/:id/reviews, /api/products/related/:id","Product, Review","Detailed view of a single product with images, description, specifications, reviews, and related products",No
Cart,/cart,CartPage.jsx,"Layout.jsx, CartItemList.jsx, OrderSummary.jsx, PromoCodeInput.jsx, CheckoutButton.jsx","/api/carts/, /api/carts/:id, /api/apply_promo","Cart, CartItem, Product","Shopping cart page showing selected items, quantities, prices, and checkout options",Yes
Checkout,/checkout,CheckoutPage.jsx,"Layout.jsx, ShippingForm.jsx, BillingForm.jsx, PaymentMethodSelector.jsx, OrderSummary.jsx","/api/shipping_options, /api/payment, /api/orders","Order, OrderLine, Cart","Checkout process with shipping, billing, and payment options",Yes
Order Confirmation,/order-confirmation,OrderConfirmationPage.jsx,"Layout.jsx, OrderConfirmationMessage.jsx","/api/orders/:id","Order, OrderLine","Confirmation page after successful order placement",Yes
Login,/login,LoginPage.jsx,"Layout.jsx, LoginForm.jsx, SocialLoginButtons.jsx","/api/auth/login/, /api/auth/social_login/","User, Credential","User login page with email/password and social login options",No
Registration,/register,RegistrationPage.jsx,"Layout.jsx, RegistrationForm.jsx, SocialSignupButtons.jsx","/api/auth/register/, /api/auth/social_signup/","User, Credential","New user registration page",No
User Profile,/profile,UserProfilePage.jsx,"Layout.jsx, UserProfileInfo.jsx, AccountSettingsForm.jsx, OrderHistory.jsx, AddressList.jsx, PaymentMethods.jsx","/api/users/:id, /api/users/:id/orders, /api/users/:id (PUT)","User, Order, OrderLine","User profile page with account settings, order history, and saved information",Yes
Contact Us,/contact,ContactUsPage.jsx,"Layout.jsx, ContactForm.jsx",None,None,"Contact form for customer inquiries",No
FAQ,/faq,FAQPage.jsx,"Layout.jsx, FAQSection.jsx",None,None,"Frequently asked questions page",No
Admin Dashboard,/admin,AdminDashboardPage.jsx,"Layout.jsx, DashboardContent.jsx, Sidebar.jsx, ProductManagementTable.jsx, OrderManagementTable.jsx, UserManagementTable.jsx","/api/products, /api/admin/orders, /api/users","Product, Order, User","Admin dashboard with product, order, and user management",Yes (Admin)
Product Moderation,/admin/moderation,ProductModerationPage.jsx,"Layout.jsx, ProductModerationQueue.jsx, ModerationForm.jsx","/api/moderation, /api/moderation (POST)","Product, ProductModerationLog","Admin page for moderating product listings",Yes (Admin)
Forgot Password,(not in routes),ForgotPasswordPage.jsx,"Layout.jsx","/api/auth/forgot-password/","User, Credential","Password recovery page",No
Order Management,(not in routes),OrderManagementPage.jsx,"Layout.jsx, OrderManagementTable.jsx","/api/admin/orders, /api/orders/:id (PUT), /api/orders/:id/refund","Order, OrderLine","Admin page for managing orders",Yes (Admin)

API Endpoints,HTTP Method,URL,Controller/View,Description,Required Authentication,Request Parameters,Response Data
Products - List,GET,/api/products/,ProductViewSet,"Get all active products, with optional filtering and sorting",No,"query: filter, sort","List of products with details"
Products - Detail,GET,/api/products/:id/,ProductViewSet,Get details of a specific product,No,path: product_id,"Product details including specifications"
Products - Reviews,GET,/api/products/:id/reviews/,ProductViewSet,Get reviews for a specific product,No,path: product_id,"List of reviews with ratings"
Products - Rating,GET,/api/products/:id/rating/,ProductViewSet,Get average rating for a product,No,path: product_id,"Average rating value"
Products - Create,POST,/api/products/,ProductViewSet,Create a new product,Yes (Admin),"product details (title, price, etc.)","Created product details"
Products - Update,PUT,/api/products/:id/,ProductViewSet,Update an existing product,Yes (Admin),"path: product_id, updated product details","Updated product details"
Products - Delete,DELETE,/api/products/:id/,ProductViewSet,Delete a product,Yes (Admin),path: product_id,"Success message"
Categories - List,GET,/api/categories/,CategoryViewSet,Get all categories,No,None,"List of categories"
Categories - Products,GET,/api/categories/:id/products/,CategoryViewSet,Get products in a specific category,No,path: category_id,"List of products in category"
Categories - Create,POST,/api/categories/,CategoryViewSet,Create a new category,Yes (Admin),"category details (name, description)","Created category details"
Cart - View,GET,/api/carts/,CartViewSet,Get user's cart,Yes,None,"Cart with items"
Cart - Add Item,POST,/api/carts/:id/add_item/,CartViewSet,Add item to cart,Yes,"path: cart_id, product_id, quantity","Updated cart"
Cart - Remove Item,POST,/api/carts/:id/remove_item/,CartViewSet,Remove item from cart,Yes,"path: cart_id, item_id","Updated cart"
Cart - Update Item,POST,/api/carts/:id/update_item/,CartViewSet,Update item quantity,Yes,"path: cart_id, item_id, quantity","Updated cart"
Cart - Checkout,POST,/api/carts/:id/checkout/,CartViewSet,Convert cart to order,Yes,"path: cart_id","Order ID and success message"
Orders - List,GET,/api/orders/,OrderViewSet,Get user's orders,Yes,None,"List of user's orders"
Orders - Cancel,POST,/api/orders/:id/cancel/,OrderViewSet,Cancel an order,Yes,path: order_id,"Updated order"
Admin Orders - List,GET,/api/admin/orders/,AdminOrderViewSet,Get all orders (admin),Yes (Admin),None,"List of all orders"
Admin Orders - Update Status,POST,/api/admin/orders/:id/update_status/,AdminOrderViewSet,Update order status,Yes (Admin),"path: order_id, payment_status, delivery_status","Updated order"
Reviews - List,GET,/api/reviews/,ReviewViewSet,Get all reviews,No,None,"List of reviews"
Reviews - Create,POST,/api/reviews/,ReviewViewSet,Create a product review,Yes,"product_id, rating, comment","Created review"
Offers - List,GET,/api/offers/,OfferViewSet,Get all active offers,No,None,"List of offers"
Offers - Active,GET,/api/offers/active/,OfferViewSet,Get currently active offers,No,None,"List of active offers"
User Behavior,POST,/api/track/,UserBehaviorView,Track user behavior,Yes,"action, product_id, metadata","Tracked behavior details"
Home Page Data,GET,/api/home/<USER>"Featured products, categories, offers"
Search,GET,/api/search/,SearchView,Search products,No,"query: q, category","Search results"
Register,POST,/api/auth/register/,RegisterView,Register new user,No,"username, email, password","User details with tokens"
Login,POST,/api/auth/login/,LoginView,Login user,No,"username, password","User details with tokens"
Token Refresh,POST,/api/auth/refresh/,TokenRefreshView,Refresh authentication token,No,refresh token,"New access token"
Moderation - List,GET,/api/moderation/,ProductModerationLogViewSet,Get moderation logs,Yes (Admin),None,"List of moderation logs"
Moderation - Action,POST,/api/moderation/moderate/,ProductModerationLogViewSet,Take moderation action,Yes (Admin),"product_id, action, comments","Moderation log details"

Database Models,Fields,Relationships,Description
User,"id, username, email, password, phone, avatar, locale, bio, company, email_validated, phone_validated, last_login, created_at, updated_at","has many: orders, carts, reviews, user_roles, credentials, social_profiles, behaviors","Extended user model with profile information"
Role,"id, name, description","has many: user_roles","User roles for permission management"
UserRole,"id, user_id, role_id, assigned_at","belongs to: user, role","Many-to-many relationship between users and roles"
Credential,"id, user_id, provider_id, provider_key, password_hash, password_salt, created_at","belongs to: user","Authentication credentials"
SocialProfile,"id, user_id, platform, platform_user, created_at","belongs to: user","Social media profiles linked to user accounts"
Category,"id, parent_category_id, slug, name, description, tags, created_at, updated_at","belongs to: parent_category, has many: subcategories, products","Product categories with hierarchical structure"
Product,"id, category_id, title, slug, summary, description, picture, price, discount_type, discount_value, tags, stock_quantity, is_active, created_at, updated_at","belongs to: category, has many: reviews, offers, moderation_logs, user_behaviors","Product information"
ProductModerationLog,"id, product_id, moderator_id, action, comments, timestamp","belongs to: product, moderator (user)","Logs of product moderation actions"
Cart,"id, created_by_id, status, created_at, updated_at","belongs to: user, has many: items","Shopping cart"
CartItem,"id, cart_id, product_id, price, quantity, created_at","belongs to: cart, product","Items in shopping cart"
Order,"id, user_id, total_price, payment_status, delivery_status, created_at","belongs to: user, has many: order_lines","Customer order"
OrderLine,"id, order_id, product_id, price, quantity","belongs to: order, product","Line items in an order"
Review,"id, user_id, product_id, rating, comment, created_at","belongs to: user, product","Product reviews and ratings"
PersonalizedRecommendation,"id, user_id, product_id, recommendation_score, created_at","belongs to: user, product","Personalized product recommendations"
Offer,"id, product_id, name, offer_type, discount_value, start_date, end_date, is_festival, is_active","belongs to: product","Special offers and discounts"
UserBehavior,"id, user_id, action, product_id, metadata, action_time","belongs to: user, product","Tracking of user interactions"
