<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eCommerce React App - Preview Pages</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-12 px-4">
        <h1 class="text-3xl font-bold mb-8 text-center">eCommerce React App - Preview Pages</h1>
        
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8">
            <p class="mb-6 text-gray-600">
                This is a static preview of the eCommerce React application. Since we're having issues with npm and running the React app directly,
                these static HTML pages have been created to help you evaluate the layout and design.
            </p>
            
            <h2 class="text-xl font-semibold mb-4">Available Preview Pages:</h2>
            
            <ul class="space-y-4">
                <li>
                    <a href="preview.html" class="block bg-blue-50 p-4 rounded-lg hover:bg-blue-100 transition">
                        <div class="font-semibold text-blue-700">Home Page</div>
                        <div class="text-sm text-gray-600 mt-1">Preview the main landing page with featured products, categories, and offers</div>
                    </a>
                </li>
                <li>
                    <a href="product-detail.html" class="block bg-blue-50 p-4 rounded-lg hover:bg-blue-100 transition">
                        <div class="font-semibold text-blue-700">Product Detail Page</div>
                        <div class="text-sm text-gray-600 mt-1">View a sample product detail page with images, description, reviews, and related products</div>
                    </a>
                </li>
                <li>
                    <a href="cart.html" class="block bg-blue-50 p-4 rounded-lg hover:bg-blue-100 transition">
                        <div class="font-semibold text-blue-700">Shopping Cart Page</div>
                        <div class="text-sm text-gray-600 mt-1">See the shopping cart layout with product items, quantity controls, and order summary</div>
                    </a>
                </li>
            </ul>
            
            <div class="mt-8 p-4 bg-yellow-50 rounded-lg">
                <h3 class="font-semibold text-yellow-800">Note:</h3>
                <p class="text-yellow-700 mt-1">
                    These are static HTML pages and do not have the full functionality of the React application.
                    They are meant to give you a visual representation of the layout and design.
                </p>
            </div>
            
            <div class="mt-6 text-center">
                <p class="text-gray-600">
                    To run the full React application, you would need to resolve the npm installation issues
                    and then run <code class="bg-gray-200 px-2 py-1 rounded">npm start</code>.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
