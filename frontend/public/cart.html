<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - eCommerce React App Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-md py-4">
        <div class="container mx-auto flex justify-between items-center px-4">
            <a href="preview.html" class="text-2xl font-bold">MyEcommerce</a>
            <ul class="flex space-x-4">
                <li><a href="preview.html" class="text-gray-600 hover:text-blue-500">Home</a></li>
                <li><a href="#" class="text-gray-600 hover:text-blue-500">Products</a></li>
                <li><a href="cart.html" class="text-blue-500 font-medium">Cart</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto py-12 px-4">
        <h1 class="text-3xl font-bold mb-8">Your Cart</h1>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Cart items list -->
            <div class="lg:col-span-2">
                <!-- Cart Item 1 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-4 flex flex-col md:flex-row items-center">
                    <div class="w-24 h-24 bg-gray-300 rounded-md flex-shrink-0"></div>
                    <div class="md:ml-6 flex-grow mt-4 md:mt-0">
                        <h3 class="text-lg font-semibold">Premium Wireless Headphones</h3>
                        <p class="text-gray-600">$199.99</p>
                    </div>
                    <div class="flex items-center mt-4 md:mt-0">
                        <button class="bg-gray-200 px-3 py-1 rounded-l">-</button>
                        <span class="bg-white px-4 py-1 border-t border-b">1</span>
                        <button class="bg-gray-200 px-3 py-1 rounded-r">+</button>
                        <button class="ml-4 text-red-500 hover:text-red-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Cart Item 2 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-4 flex flex-col md:flex-row items-center">
                    <div class="w-24 h-24 bg-gray-300 rounded-md flex-shrink-0"></div>
                    <div class="md:ml-6 flex-grow mt-4 md:mt-0">
                        <h3 class="text-lg font-semibold">Bluetooth Speaker</h3>
                        <p class="text-gray-600">$129.99</p>
                    </div>
                    <div class="flex items-center mt-4 md:mt-0">
                        <button class="bg-gray-200 px-3 py-1 rounded-l">-</button>
                        <span class="bg-white px-4 py-1 border-t border-b">2</span>
                        <button class="bg-gray-200 px-3 py-1 rounded-r">+</button>
                        <button class="ml-4 text-red-500 hover:text-red-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-4">Order Summary</h2>
                    <div class="border-t border-b py-4">
                        <div class="flex justify-between mb-2">
                            <span>Subtotal</span>
                            <span>$459.97</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span>Shipping</span>
                            <span>$9.99</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span>Tax</span>
                            <span>$36.80</span>
                        </div>
                    </div>
                    <div class="flex justify-between font-semibold text-lg mt-4">
                        <span>Total</span>
                        <span>$506.76</span>
                    </div>
                    
                    <!-- Promo Code -->
                    <div class="mt-6">
                        <label class="block text-gray-700 mb-2">Promo Code</label>
                        <div class="flex">
                            <input type="text" class="border rounded-l px-4 py-2 w-full" placeholder="Enter code">
                            <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-r">Apply</button>
                        </div>
                    </div>
                    
                    <!-- Checkout Button -->
                    <button class="bg-blue-500 text-white py-3 px-6 rounded-lg mt-6 w-full hover:bg-blue-600">
                        Proceed to Checkout
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6">
        <div class="container mx-auto px-4">
            <ul class="flex justify-center space-x-4">
                <li><a href="preview.html" class="hover:text-gray-400">Home</a></li>
                <li><a href="#" class="hover:text-gray-400">Contact</a></li>
                <li><a href="#" class="hover:text-gray-400">About</a></li>
                <li><a href="#" class="hover:text-gray-400">FAQ</a></li>
            </ul>
            <p class="text-center mt-4">© 2024 MyEcommerce. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
