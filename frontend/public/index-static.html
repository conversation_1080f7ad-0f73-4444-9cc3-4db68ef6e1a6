<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eCommerce React App</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/babel-standalone@6/babel.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body>
    <div id="root"></div>
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold mb-4">eCommerce React App</h1>
        <p class="mb-4">This is a static version of the eCommerce React application.</p>
        <p>To run the full React application, you need to:</p>
        <ol class="list-decimal pl-8 mb-4">
            <li>Install Node.js and npm</li>
            <li>Run <code class="bg-gray-200 px-2 py-1 rounded">npm install</code> to install dependencies</li>
            <li>Run <code class="bg-gray-200 px-2 py-1 rounded">npm start</code> to start the development server</li>
        </ol>
        <div class="bg-blue-500 text-white p-8 text-center rounded-lg shadow-lg">
            <h2 class="text-4xl font-bold mb-2">Welcome to MyEcommerce!</h2>
            <p class="text-lg">Shop our best offers and featured products</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div class="bg-white p-4 shadow rounded">
                <h3 class="text-xl font-bold">Product 1</h3>
                <p class="text-gray-600">$19.99</p>
                <button class="mt-2 bg-blue-500 text-white px-4 py-2 rounded">Add to Cart</button>
            </div>
            <div class="bg-white p-4 shadow rounded">
                <h3 class="text-xl font-bold">Product 2</h3>
                <p class="text-gray-600">$29.99</p>
                <button class="mt-2 bg-blue-500 text-white px-4 py-2 rounded">Add to Cart</button>
            </div>
            <div class="bg-white p-4 shadow rounded">
                <h3 class="text-xl font-bold">Product 3</h3>
                <p class="text-gray-600">$39.99</p>
                <button class="mt-2 bg-blue-500 text-white px-4 py-2 rounded">Add to Cart</button>
            </div>
        </div>
    </div>
</body>
</html>
