import axios from 'axios';

// Base API URL
const API_BASE_URL = 'http://localhost:8000/api';

// Test function to fetch products
const testFetchProducts = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/products/`);
    console.log('Products fetched successfully:');
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching products:', error);
    return null;
  }
};

// Test function to fetch categories
const testFetchCategories = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/categories/`);
    console.log('Categories fetched successfully:');
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return null;
  }
};

// Test function to fetch offers
const testFetchOffers = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/offers/`);
    console.log('Offers fetched successfully:');
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching offers:', error);
    return null;
  }
};

// Run all tests
const runAllTests = async () => {
  console.log('Running API tests...');
  
  // Test products API
  const products = await testFetchProducts();
  
  // Test categories API
  const categories = await testFetchCategories();
  
  // Test offers API
  const offers = await testFetchOffers();
  
  // Check if all tests passed
  if (products && categories && offers) {
    console.log('All API tests passed! Frontend and backend are properly connected.');
    return true;
  } else {
    console.error('Some API tests failed. Check the errors above.');
    return false;
  }
};

export { runAllTests, testFetchProducts, testFetchCategories, testFetchOffers };
