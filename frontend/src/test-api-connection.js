import { 
  fetchProducts, 
  fetchCategories, 
  fetchOffers,
  fetchProductDetails,
  fetchRelatedProducts,
  fetchProductReviews
} from './services/api';

// Test function to fetch products
const testFetchProducts = async () => {
  try {
    const response = await fetchProducts();
    console.log('Products fetched successfully:');
    console.log(`Found ${response.data.count} products`);
    return true;
  } catch (error) {
    console.error('Error fetching products:', error.message);
    return false;
  }
};

// Test function to fetch categories
const testFetchCategories = async () => {
  try {
    const response = await fetchCategories();
    console.log('Categories fetched successfully:');
    console.log(`Found ${response.data.count} categories`);
    return true;
  } catch (error) {
    console.error('Error fetching categories:', error.message);
    return false;
  }
};

// Test function to fetch offers
const testFetchOffers = async () => {
  try {
    const response = await fetchOffers();
    console.log('Offers fetched successfully:');
    console.log(`Found ${response.data.count} offers`);
    return true;
  } catch (error) {
    console.error('Error fetching offers:', error.message);
    return false;
  }
};

// Test function to fetch product details
const testFetchProductDetails = async (productId) => {
  try {
    const response = await fetchProductDetails(productId);
    console.log('Product details fetched successfully:');
    console.log(`Product: ${response.data.title}`);
    return true;
  } catch (error) {
    console.error('Error fetching product details:', error.message);
    return false;
  }
};

// Run all tests
const runAllTests = async () => {
  console.log('Running API tests...');
  
  // Test products API
  const productsResult = await testFetchProducts();
  
  // Test categories API
  const categoriesResult = await testFetchCategories();
  
  // Test offers API
  const offersResult = await testFetchOffers();
  
  // Test product details API (if products are available)
  let productDetailsResult = true;
  try {
    const productsResponse = await fetchProducts();
    if (productsResponse.data.results.length > 0) {
      const firstProductId = productsResponse.data.results[0].id;
      productDetailsResult = await testFetchProductDetails(firstProductId);
    }
  } catch (error) {
    console.error('Error testing product details:', error.message);
    productDetailsResult = false;
  }
  
  // Check if all tests passed
  if (productsResult && categoriesResult && offersResult && productDetailsResult) {
    console.log('\n✅ All API tests passed! Frontend and backend are properly connected.');
    return true;
  } else {
    console.error('\n❌ Some API tests failed. Check the errors above.');
    return false;
  }
};

export { runAllTests, testFetchProducts, testFetchCategories, testFetchOffers, testFetchProductDetails };
