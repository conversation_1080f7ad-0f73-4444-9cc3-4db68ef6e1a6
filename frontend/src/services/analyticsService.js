import axios from 'axios';

// Base API URL, can be set from environment variables
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';

// Axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * Track user behavior in the backend
 * @param {Object} data - The tracking data
 * @param {string} data.userId - The user ID
 * @param {string} data.action - The action type (e.g., 'page_view', 'click', 'add_to_cart')
 * @param {string} data.productId - Optional product ID if action is related to a product
 * @param {Object} data.metadata - Additional data about the action
 * @returns {Promise} - The API response
 */
export const trackUserBehavior = async (data) => {
  try {
    return await api.post('/track/', {
      user_id: data.userId,
      action: data.action,
      product_id: data.productId,
      metadata: data.metadata
    });
  } catch (error) {
    console.error('Error tracking user behavior:', error);
    // Don't throw the error to prevent breaking the user experience
    return null;
  }
};

/**
 * Get analytics data for a specific user
 * @param {string} userId - The user ID
 * @returns {Promise} - The API response with user analytics data
 */
export const getUserAnalytics = async (userId) => {
  return await api.get(`/analytics/user/${userId}`);
};

/**
 * Get general analytics data (admin only)
 * @returns {Promise} - The API response with general analytics data
 */
export const getGeneralAnalytics = async () => {
  return await api.get('/analytics/general');
};

export default {
  trackUserBehavior,
  getUserAnalytics,
  getGeneralAnalytics
};
