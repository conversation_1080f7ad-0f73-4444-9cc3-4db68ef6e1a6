import axios from 'axios';

// Base API URL, can be set from environment variables
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';

// Axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Order Service
const orderService = {
  getOrders: () => api.get('/admin/orders'),
  updateOrderStatus: (orderId, status) => api.put(`/orders/${orderId}`, status),
  getUserOrders: (userId) => api.get(`/orders?user=${userId}`),
  getOrderDetails: (orderId) => api.get(`/orders/${orderId}`),
  processRefund: (orderId) => api.post(`/orders/${orderId}/refund`),
};

export default orderService;
