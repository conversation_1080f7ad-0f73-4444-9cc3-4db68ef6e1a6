import React, { useState } from 'react';
import axios from 'axios';

const ModerationForm = ({ productId }) => {
  const [action, setAction] = useState('approve'); // Default action is approve
  const [comments, setComments] = useState('');

  const handleModeration = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post('/api/admin/moderation_action', {
        productId,
        action,
        comments,
      });
      console.log('Moderation action successful', response.data);
      // Handle post-action feedback, like clearing the form or refreshing the queue
    } catch (error) {
      console.error('Error during moderation action:', error);
    }
  };

  return (
    <form onSubmit={handleModeration} className="space-y-6">
      <div>
        <label htmlFor="action" className="block text-sm font-medium text-gray-700">Action</label>
        <select
          id="action"
          value={action}
          onChange={(e) => setAction(e.target.value)}
          className="input-field w-full"
        >
          <option value="approve">Approve</option>
          <option value="reject">Reject</option>
          <option value="request_changes">Request Changes</option>
        </select>
      </div>

      <div>
        <label htmlFor="comments" className="block text-sm font-medium text-gray-700">Comments</label>
        <textarea
          id="comments"
          value={comments}
          onChange={(e) => setComments(e.target.value)}
          className="input-field w-full"
        />
      </div>

      <button type="submit" className="btn-primary w-full">Submit Action</button>
    </form>
  );
};

export default ModerationForm;
