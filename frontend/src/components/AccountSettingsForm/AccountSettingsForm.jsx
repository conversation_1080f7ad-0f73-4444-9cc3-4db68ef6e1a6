import React, { useState } from 'react';

const AccountSettingsForm = ({ profile, onProfileUpdate }) => {
  const [updatedProfile, setUpdatedProfile] = useState({
    fullName: profile.fullName || '',
    email: profile.email || '',
  });

  const handleChange = (e) => {
    setUpdatedProfile({
      ...updatedProfile,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onProfileUpdate(updatedProfile);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h3 className="text-xl font-semibold">Account Settings</h3>
      <input
        type="text"
        name="fullName"
        value={updatedProfile.fullName}
        onChange={handleChange}
        placeholder="Full Name"
        className="input-field"
      />
      <input
        type="email"
        name="email"
        value={updatedProfile.email}
        onChange={handleChange}
        placeholder="Email"
        className="input-field"
      />
      <button
        type="submit"
        className="w-full bg-blue-600 text-white py-2 rounded-lg shadow-md hover:bg-blue-700"
      >
        Update Profile
      </button>
    </form>
  );
};

export default AccountSettingsForm;
