import React, { useState } from 'react';
import axios from 'axios';

const RegistrationForm = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const handleRegister = async (e) => {
    e.preventDefault();

    try {
      const response = await axios.post('/api/auth/register', { name, email, password });
      // Handle success - perhaps redirect the user
      console.log('Registration successful', response.data);
    } catch (error) {
      setErrorMessage('Error during registration');
      console.error('Registration error:', error);
    }
  };

  return (
    <form onSubmit={handleRegister} className="space-y-6">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700">Name</label>
        <input
          type="text"
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="input-field w-full"
          required
        />
      </div>
      
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="input-field w-full"
          required
        />
      </div>
      
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700">Password</label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="input-field w-full"
          required
        />
      </div>
      
      {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}
      
      <button type="submit" className="btn-primary w-full">Register</button>
    </form>
  );
};

export default RegistrationForm;
