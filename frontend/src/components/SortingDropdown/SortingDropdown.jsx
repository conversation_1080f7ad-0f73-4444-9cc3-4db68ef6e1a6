import React from 'react';

const SortingDropdown = ({ sortBy, onSortChange }) => {
  return (
    <div className="relative inline-block">
      <select
        value={sortBy}
        onChange={(e) => onSortChange(e.target.value)}
        className="input w-full"
      >
        <option value="popularity">Sort by Popularity</option>
        <option value="priceLowHigh">Price: Low to High</option>
        <option value="priceHighLow">Price: High to Low</option>
      </select>
    </div>
  );
};

export default SortingDropdown;
