# Frontend Components Structure

This directory contains reusable components for the eCommerce frontend application.

## Core Components

### Layout Component

The `Layout` component provides a consistent structure for all pages in the application. It includes:

- Navbar (header)
- Main content area
- Footer
- Toast notifications

Usage:

```jsx
import Layout from '../components/Layout';

const MyPage = () => {
  return (
    <Layout>
      {/* Your page content here */}
    </Layout>
  );
};
```

### Navbar Component

The `Navbar` component is the main navigation header used across all pages.

### Footer Component

The `Footer` component is the consistent footer used across all pages.

## Product Components

### ProductCard

Displays a product in a card format, used in product listings and related products sections.

### ProductList

Displays a grid of ProductCard components with optional filtering and sorting.

## Form Components

Various form components used throughout the application.

## Best Practices

1. **Use the Layout component** for all pages to maintain consistency
2. **Keep components small and focused** on a single responsibility
3. **Use props for configuration** rather than hardcoding values
4. **Document component props** with JSDoc comments
5. **Use consistent naming conventions** for components and their files
