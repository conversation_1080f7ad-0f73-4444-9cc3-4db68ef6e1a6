import React from 'react';

const SortDropdown = ({ sortBy, onSortChange }) => {
  return (
    <div className="relative inline-block">
      <select
        className="form-select"
        value={sortBy}
        onChange={(e) => onSortChange(e.target.value)}
      >
        <option value="popularity">Sort by Popularity</option>
        <option value="priceLowHigh">Price: Low to High</option>
        <option value="priceHighLow">Price: High to Low</option>
      </select>
    </div>
  );
};

export default SortDropdown;
