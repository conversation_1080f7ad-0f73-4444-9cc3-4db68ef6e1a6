
import React from 'react';

const Footer = ({ links }) => {
  return (
    <footer className="bg-gray-800 text-white py-6">
      <div className="container mx-auto">
        <ul className="flex justify-center space-x-4">
          {links.map((link, index) => (
            <li key={index}>
              <a href={link.href} className="hover:text-gray-400">{link.name}</a>
            </li>
          ))}
        </ul>
        <p className="text-center mt-4">© 2024 MyEcommerce. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;
