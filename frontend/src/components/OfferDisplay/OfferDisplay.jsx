import React from 'react';

const OfferDisplay = ({ offers }) => {
  if (!offers || offers.length === 0) {
    return null;
  }

  return (
    <div className="bg-green-100 p-4 rounded-lg mb-4">
      <h4 className="text-lg font-semibold text-green-800">Special Offers</h4>
      <ul className="list-disc pl-5 text-green-700">
        {offers.map((offer) => (
          <li key={offer.id}>{offer.description}</li>
        ))}
      </ul>
    </div>
  );
};

export default OfferDisplay;
