import React, { useState } from 'react';

const PromoCodeInput = ({ onApplyPromo, promoError }) => {
  const [promoCode, setPromoCode] = useState('');

  const handlePromoChange = (e) => {
    setPromoCode(e.target.value);
  };

  const handleApplyPromo = () => {
    onApplyPromo(promoCode);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold">Promo Code</h3>
      <input
        type="text"
        value={promoCode}
        onChange={handlePromoChange}
        placeholder="Enter Promo Code"
        className="input-field"
      />
      {promoError && <p className="text-red-500">{promoError}</p>}
      <button
        onClick={handleApplyPromo}
        className="w-full bg-yellow-600 text-white py-2 rounded-lg shadow-md"
      >
        Apply Promo
      </button>
    </div>
  );
};

export default PromoCodeInput;
