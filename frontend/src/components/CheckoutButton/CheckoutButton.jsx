import React from 'react';
import { useHistory } from 'react-router-dom';

const CheckoutButton = () => {
  const history = useHistory();

  const handleCheckout = () => {
    history.push('/checkout');
  };

  return (
    <button
      onClick={handleCheckout}
      className="w-full bg-blue-600 text-white py-3 rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      Proceed to Checkout
    </button>
  );
};

export default CheckoutButton;
