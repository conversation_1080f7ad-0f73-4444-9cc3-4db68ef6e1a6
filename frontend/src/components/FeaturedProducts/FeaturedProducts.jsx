import React, { useEffect, useState } from 'react';
import { fetchFeaturedProducts } from '../services/api'; // Fetch from API
import ProductCard from '../components/ProductCard/ProductCard';

const FeaturedProducts = () => {
  const [products, setProducts] = useState([]);

  useEffect(() => {
    const getFeaturedProducts = async () => {
      const response = await fetchFeaturedProducts();
      setProducts(response.data);
    };
    getFeaturedProducts();
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};

export default FeaturedProducts;
