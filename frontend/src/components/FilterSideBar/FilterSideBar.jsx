import React, { useState } from 'react';

const FilterSidebar = ({ onFilterChange }) => {
  const [category, setCategory] = useState('');
  const [price, setPrice] = useState(1000);

  const handleFilter = () => {
    onFilterChange({ category, price });
  };

  return (
    <div className="bg-white p-4 shadow-md rounded-lg">
      <h3 className="text-xl font-semibold mb-4">Filters</h3>
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700">Category</label>
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value)}
          className="input w-full"
        >
          <option value="">All</option>
          <option value="electronics">Electronics</option>
          <option value="fashion">Fashion</option>
          <option value="home">Home</option>
        </select>
      </div>
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700">Price Range</label>
        <input
          type="range"
          min="0"
          max="1000"
          value={price}
          onChange={(e) => setPrice(e.target.value)}
          className="w-full"
        />
        <span>${price}</span>
      </div>
      <button onClick={handleFilter} className="btn-primary w-full">Apply Filters</button>
    </div>
  );
};

export default FilterSidebar;
