import React, { useState } from 'react';

const PaymentMethod = ({ onPaymentSubmit }) => {
  const [selectedPayment, setSelectedPayment] = useState('creditCard');

  const handlePaymentChange = (e) => {
    setSelectedPayment(e.target.value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onPaymentSubmit(selectedPayment);
  };

  return (
    <form onSubmit={handleSubmit} className="card bg-white shadow-md p-6 rounded-lg mt-8">
      <h2 className="text-xl font-semibold mb-4">Payment Method</h2>
      <div className="grid gap-4">
        <label className="flex items-center">
          <input
            type="radio"
            value="creditCard"
            checked={selectedPayment === 'creditCard'}
            onChange={handlePaymentChange}
            className="mr-2"
          />
          Credit Card
        </label>
        <label className="flex items-center">
          <input
            type="radio"
            value="paypal"
            checked={selectedPayment === 'paypal'}
            onChange={handlePaymentChange}
            className="mr-2"
          />
          PayPal
        </label>
        <label className="flex items-center">
          <input
            type="radio"
            value="bankTransfer"
            checked={selectedPayment === 'bankTransfer'}
            onChange={handlePaymentChange}
            className="mr-2"
          />
          Bank Transfer
        </label>
      </div>
      <button type="submit" className="btn-primary mt-4 w-full">Confirm Payment Method</button>
    </form>
  );
};

export default PaymentMethod;
