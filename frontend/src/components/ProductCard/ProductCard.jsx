import React from 'react';

const ProductCard = ({ product }) => {
  return (
    <div className="card shadow-md p-4 rounded-lg hover:shadow-lg">
      <img src={product.imageUrl} alt={product.name} className="w-full h-48 object-cover rounded-lg" />
      <div className="mt-4">
        <h3 className="text-lg font-semibold">{product.name}</h3>
        <p className="text-gray-500">${product.price.toFixed(2)}</p>
        <button className="btn-primary mt-4 w-full">Add to Cart</button>
      </div>
    </div>
  );
};

export default ProductCard;
