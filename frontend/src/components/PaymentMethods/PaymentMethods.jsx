import React from 'react';

const PaymentMethods = ({ paymentMethods }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Payment Methods</h3>
      {paymentMethods.length > 0 ? (
        <ul className="space-y-4">
          {paymentMethods.map((method, index) => (
            <li key={index} className="border p-4 rounded-lg">
              <p>Card Number: **** **** **** {method.last4Digits}</p>
              <p>Expiry: {method.expiryMonth}/{method.expiryYear}</p>
            </li>
          ))}
        </ul>
      ) : (
        <p>No payment methods found.</p>
      )}
    </div>
  );
};

export default PaymentMethods;
