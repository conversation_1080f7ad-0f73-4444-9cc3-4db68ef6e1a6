import React from 'react';
import axios from 'axios';

const SocialLoginButtons = () => {
  const handleSocialLogin = async (provider) => {
    try {
      // Call API to login via the chosen provider
      const response = await axios.post('/api/auth/social_login', { provider });
      // Handle success - perhaps redirect the user
      console.log('Social login successful', response.data);
    } catch (error) {
      console.error('Social login error:', error);
    }
  };

  return (
    <div className="space-y-4">
      <button
        onClick={() => handleSocialLogin('google')}
        className="btn-outline w-full"
      >
        Login with Google
      </button>
      
      <button
        onClick={() => handleSocialLogin('facebook')}
        className="btn-outline w-full"
      >
        Login with Facebook
      </button>
    </div>
  );
};

export default SocialLoginButtons;
