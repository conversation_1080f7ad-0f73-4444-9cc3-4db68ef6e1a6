import React from 'react';

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  const pages = [...Array(totalPages).keys()].map((p) => p + 1);

  return (
    <div className="flex justify-center space-x-2 mt-8">
      {pages.map((page) => (
        <button
          key={page}
          className={`py-2 px-4 rounded-lg ${page === currentPage ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          onClick={() => onPageChange(page)}
        >
          {page}
        </button>
      ))}
    </div>
  );
};

export default Pagination;
