import React from 'react';
import axios from 'axios';

const SocialSignupButtons = () => {
  const handleSocialSignup = async (provider) => {
    try {
      // Call API to sign up via the chosen provider
      const response = await axios.post('/api/auth/social_signup', { provider });
      // Handle success - perhaps redirect the user
      console.log('Social signup successful', response.data);
    } catch (error) {
      console.error('Social signup error:', error);
    }
  };

  return (
    <div className="space-y-4">
      <button
        onClick={() => handleSocialSignup('google')}
        className="btn-outline w-full"
      >
        Sign Up with Google
      </button>
      
      <button
        onClick={() => handleSocialSignup('facebook')}
        className="btn-outline w-full"
      >
        Sign Up with Facebook
      </button>
    </div>
  );
};

export default SocialSignupButtons;
