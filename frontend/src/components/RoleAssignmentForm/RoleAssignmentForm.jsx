import React, { useState } from 'react';

const RoleAssignmentForm = ({ users }) => {
  const [selectedUser, setSelectedUser] = useState('');
  const [role, setRole] = useState('');

  const handleRoleAssignment = async () => {
    try {
      await axios.post('/api/admin/assign_role', { userId: selectedUser, role });
      alert('Role assigned successfully');
    } catch (error) {
      console.error('Error assigning role:', error);
    }
  };

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Assign Roles</h3>
      <div>
        <label htmlFor="user-select">User</label>
        <select
          id="user-select"
          value={selectedUser}
          onChange={(e) => setSelectedUser(e.target.value)}
          className="input-field"
        >
          <option value="">Select User</option>
          {users.map((user) => (
            <option key={user.id} value={user.id}>
              {user.name}
            </option>
          ))}
        </select>
      </div>
      <div>
        <label htmlFor="role-select">Role</label>
        <select
          id="role-select"
          value={role}
          onChange={(e) => setRole(e.target.value)}
          className="input-field"
        >
          <option value="">Select Role</option>
          <option value="admin">Admin</option>
          <option value="moderator">Moderator</option>
          <option value="user">User</option>
        </select>
      </div>
      <button className="btn-primary" onClick={handleRoleAssignment}>
        Assign Role
      </button>
    </div>
  );
};

export default RoleAssignmentForm;
