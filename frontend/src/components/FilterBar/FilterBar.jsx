import React from 'react';

const FilterBar = ({ filters, onFilterChange }) => {
  return (
    <div className="bg-white shadow-md p-4 rounded-lg flex space-x-4">
      <select
        className="form-select"
        value={filters.category}
        onChange={(e) => onFilterChange('category', e.target.value)}
      >
        <option value="">All Categories</option>
        <option value="electronics">Electronics</option>
        <option value="fashion">Fashion</option>
        <option value="home">Home</option>
      </select>

      <input
        type="range"
        className="form-range"
        min="0"
        max="1000"
        value={filters.price}
        onChange={(e) => onFilterChange('price', e.target.value)}
      />
      <span className="text-gray-500">${filters.price}</span>
    </div>
  );
};

export default FilterBar;
