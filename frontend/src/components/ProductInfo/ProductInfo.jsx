import React from 'react';

const ProductInfo = ({ product, onAddToCart }) => {
  return (
    <div className="p-6 bg-white shadow-md rounded-lg">
      <h1 className="text-3xl font-semibold">{product.name}</h1>
      <p className="text-xl text-gray-700 mt-2">${product.price.toFixed(2)}</p>
      <p className="text-gray-600 mt-4">{product.description}</p>
      <button
        onClick={() => onAddToCart(product.id)}
        className="mt-6 btn-primary w-full"
      >
        Add to Cart
      </button>
    </div>
  );
};

export default ProductInfo;