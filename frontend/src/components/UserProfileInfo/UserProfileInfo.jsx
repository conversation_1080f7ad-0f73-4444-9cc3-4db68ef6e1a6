import React, { useState } from 'react';

const UserProfileInfo = ({ user, onUpdateUser }) => {
  const [editable, setEditable] = useState(false);
  const [formData, setFormData] = useState({
    name: user.name,
    email: user.email,
    password: '',
  });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleUpdate = () => {
    onUpdateUser(formData);
    setEditable(false);
  };

  return (
    <div className="card p-6">
      <h2 className="text-2xl font-semibold">Profile Information</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <label className="label">Name</label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            disabled={!editable}
            className="input w-full"
          />
        </div>
        <div>
          <label className="label">Email</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            disabled={!editable}
            className="input w-full"
          />
        </div>
        {editable && (
          <div className="col-span-2">
            <label className="label">Password (Leave blank to keep current)</label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="input w-full"
            />
          </div>
        )}
      </div>
      <div className="flex justify-end mt-6">
        {editable ? (
          <button onClick={handleUpdate} className="btn-primary mr-2">Save</button>
        ) : (
          <button onClick={() => setEditable(true)} className="btn-primary">Edit</button>
        )}
      </div>
    </div>
  );
};

export default UserProfileInfo;
