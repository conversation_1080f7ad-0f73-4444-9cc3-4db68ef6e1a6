import React from 'react';

const OrderManagementTable = ({ orders }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Order Management</h3>
      <table className="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th className="py-2 px-4 border">Order ID</th>
            <th className="py-2 px-4 border">Customer</th>
            <th className="py-2 px-4 border">Total</th>
            <th className="py-2 px-4 border">Status</th>
            <th className="py-2 px-4 border">Actions</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order) => (
            <tr key={order.id}>
              <td className="py-2 px-4 border">{order.id}</td>
              <td className="py-2 px-4 border">{order.customerName}</td>
              <td className="py-2 px-4 border">${order.total}</td>
              <td className="py-2 px-4 border">{order.status}</td>
              <td className="py-2 px-4 border">
                <button className="text-blue-500">Update Status</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default OrderManagementTable;
