
import React, { useState } from 'react';

const FAQSection = ({ question, answer }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleFAQ = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="mb-4">
      <div
        className="flex justify-between items-center p-4 bg-gray-100 rounded cursor-pointer"
        onClick={toggleFAQ}
      >
        <h3 className="font-semibold text-lg">{question}</h3>
        <span>{isOpen ? '-' : '+'}</span>
      </div>
      {isOpen && (
        <div className="p-4 bg-white rounded shadow-md">
          <p>{answer}</p>
        </div>
      )}
    </div>
  );
};

export default FAQSection;
