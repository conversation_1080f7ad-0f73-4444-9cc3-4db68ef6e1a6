import React, { useState } from 'react';

const BillingForm = ({ onBillingSubmit }) => {
  const [billingInfo, setBillingInfo] = useState({
    fullName: '',
    address: '',
    city: '',
    postalCode: '',
  });

  const handleChange = (e) => {
    setBillingInfo({
      ...billingInfo,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onBillingSubmit(billingInfo);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h3 className="text-xl font-semibold">Billing Information</h3>
      <input
        type="text"
        name="fullName"
        value={billingInfo.fullName}
        onChange={handleChange}
        placeholder="Full Name"
        className="input-field"
      />
      <input
        type="text"
        name="address"
        value={billingInfo.address}
        onChange={handleChange}
        placeholder="Address"
        className="input-field"
      />
      <input
        type="text"
        name="city"
        value={billingInfo.city}
        onChange={handleChange}
        placeholder="City"
        className="input-field"
      />
      <input
        type="text"
        name="postalCode"
        value={billingInfo.postalCode}
        onChange={handleChange}
        placeholder="Postal Code"
        className="input-field"
      />
      <button type="submit" className="w-full bg-blue-600 text-white py-2 rounded-lg shadow-md">
        Save Billing Info
      </button>
    </form>
  );
};

export default BillingForm;
