import React from 'react';

const PersonalizedRecommendations = ({ recommendations }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Personalized Recommendations</h3>
      {recommendations.length > 0 ? (
        <ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {recommendations.map((product) => (
            <li key={product.id} className="border p-4 rounded-lg">
              <p>{product.name}</p>
              <p>${product.price}</p>
            </li>
          ))}
        </ul>
      ) : (
        <p>No recommendations found.</p>
      )}
    </div>
  );
};

export default PersonalizedRecommendations;
