import React, { useEffect, useState } from 'react';
import { fetchCategories } from '../services/api'; // Fetch from API

const CategoryList = () => {
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    const getCategories = async () => {
      const response = await fetchCategories();
      setCategories(response.data);
    };
    getCategories();
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-8">
      {categories.map((category) => (
        <div key={category.id} className="card shadow-lg p-4 text-center rounded-lg">
          <h2 className="text-xl font-semibold">{category.name}</h2>
        </div>
      ))}
    </div>
  );
};

export default CategoryList;
