import React from 'react';

const AddressList = ({ addresses }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Saved Addresses</h3>
      {addresses.length > 0 ? (
        <ul className="space-y-4">
          {addresses.map((address, index) => (
            <li key={index} className="border p-4 rounded-lg">
              <p>{address.fullName}</p>
              <p>{address.streetAddress}</p>
              <p>{address.city}, {address.state} {address.zipCode}</p>
            </li>
          ))}
        </ul>
      ) : (
        <p>No addresses found.</p>
      )}
    </div>
  );
};

export default AddressList;
