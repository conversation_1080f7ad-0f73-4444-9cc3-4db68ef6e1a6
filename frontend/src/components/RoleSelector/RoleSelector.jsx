import React, { useState } from 'react';

const RoleSelector = () => {
  const [role, setRole] = useState('customer');  // Default role is customer

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700">Select Your Role</label>
      <div className="flex items-center space-x-4">
        <label className="flex items-center">
          <input
            type="radio"
            name="role"
            value="customer"
            checked={role === 'customer'}
            onChange={(e) => setRole(e.target.value)}
            className="form-radio"
          />
          <span className="ml-2">Customer</span>
        </label>
        
        <label className="flex items-center">
          <input
            type="radio"
            name="role"
            value="vendor"
            checked={role === 'vendor'}
            onChange={(e) => setRole(e.target.value)}
            className="form-radio"
          />
          <span className="ml-2">Vendor</span>
        </label>
      </div>
    </div>
  );
};

export default RoleSelector;
