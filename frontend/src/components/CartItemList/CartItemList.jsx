import React from 'react';
import QuantitySelector from './QuantitySelector';

const CartItemList = ({ cartItems, onQuantityChange, onRemoveItem }) => {
  return (
    <div className="grid grid-cols-1 gap-8">
      {cartItems.map((item) => (
        <div key={item.product_id} className="flex items-center justify-between bg-white p-6 shadow-md rounded-lg">
          <div className="flex items-center">
            <img src={item.image_url} alt={item.name} className="w-24 h-24 object-cover mr-4" />
            <div>
              <h3 className="text-xl font-semibold">{item.name}</h3>
              <p className="text-gray-600">${item.price}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <QuantitySelector
              quantity={item.quantity}
              onQuantityChange={(newQuantity) => onQuantityChange(item.product_id, newQuantity)}
            />
            <button
              onClick={() => onRemoveItem(item.product_id)}
              className="text-red-500 hover:text-red-700"
            >
              Remove
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CartItemList;
