import React from 'react';

const ProductReviews = ({ reviews }) => {
  return (
    <div className="mt-8">
      <h2 className="text-2xl font-semibold mb-4">Customer Reviews</h2>
      <div className="grid grid-cols-1 gap-6">
        {reviews.map((review, index) => (
          <div key={index} className="card p-4">
            <h3 className="text-lg font-semibold">{review.author}</h3>
            <p className="text-gray-600">{review.content}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProductReviews;
