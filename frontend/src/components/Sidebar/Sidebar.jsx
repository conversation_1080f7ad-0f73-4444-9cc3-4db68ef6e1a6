import React from 'react';

const Sidebar = () => {
  return (
    <div className="w-64 h-full bg-gray-800 text-white">
      <nav className="p-4">
        <ul className="space-y-4">
          <li>
            <a href="#orders" className="hover:bg-gray-700 block px-4 py-2 rounded">Orders</a>
          </li>
          <li>
            <a href="#products" className="hover:bg-gray-700 block px-4 py-2 rounded">Products</a>
          </li>
          <li>
            <a href="#users" className="hover:bg-gray-700 block px-4 py-2 rounded">Users</a>
          </li>
          <li>
            <a href="#stats" className="hover:bg-gray-700 block px-4 py-2 rounded">Statistics</a>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;
