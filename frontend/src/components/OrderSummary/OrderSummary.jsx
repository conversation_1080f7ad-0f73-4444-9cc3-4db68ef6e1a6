import React from 'react';

const OrderSummary = ({ orderTotal, shippingOptions, onShippingSelect }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Order Summary</h3>
      <p className="text-lg">Total: ${orderTotal}</p>
      <div>
        <h4 className="font-medium">Shipping Options</h4>
        <select onChange={(e) => onShippingSelect(e.target.value)} className="input-field">
          <option value="">Select Shipping Option</option>
          {shippingOptions.map((option) => (
            <option key={option.id} value={option.id}>
              {option.name} - ${option.price}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default OrderSummary;
