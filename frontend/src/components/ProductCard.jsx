import React from 'react';
import { Link } from 'react-router-dom';
import { StarIcon, ShoppingCartIcon, HeartIcon } from '@heroicons/react/24/solid';
import { HeartIcon as HeartOutlineIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const ProductCard = ({ product, isFavorite = false }) => {
  const { id, name, price, rating, reviewCount, imageSrc, images, discount, discountPercentage, category } = product;

  // Use the first image from images array if available, otherwise fall back to imageSrc
  const displayImage = images && images.length > 0 ? images[0] : imageSrc;

  const discountedPrice = discount ? price - (price * discountPercentage) / 100 : price;

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Add to cart logic here
    toast.success(`${name} added to cart!`);
  };

  const handleToggleFavorite = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Toggle favorite logic here
    toast.success(isFavorite ? `${name} removed from favorites!` : `${name} added to favorites!`);
  };

  return (
    <div className="group relative overflow-hidden rounded-lg bg-white shadow-soft transition-all duration-300 hover:shadow-hover">
      {/* Discount Badge */}
      {discount && (
        <div className="absolute left-0 top-0 z-10 rounded-br-lg bg-red-500 px-2 py-1 text-xs font-bold text-white">
          {discountPercentage}% OFF
        </div>
      )}

      {/* Category Badge */}
      <div className="absolute right-0 top-0 z-10 rounded-bl-lg bg-gray-800/70 px-2 py-1 text-xs font-medium text-white">
        {category}
      </div>

      {/* Product Image */}
      <Link to={`/products/${id}`} className="block overflow-hidden">
        <div className="relative h-64 overflow-hidden bg-gray-200">
          {displayImage ? (
            <img
              src={displayImage}
              alt={name}
              className="h-full w-full object-cover object-center transition-transform duration-500 group-hover:scale-110"
            />
          ) : (
            <div className="flex h-full items-center justify-center bg-gray-200 text-gray-500">
              No Image
            </div>
          )}
        </div>
      </Link>

      {/* Product Info */}
      <div className="p-4">
        <Link to={`/products/${id}`} className="block">
          <h3 className="mb-1 text-lg font-semibold text-gray-900 transition-colors duration-300 group-hover:text-primary-600">
            {name}
          </h3>
        </Link>

        {/* Rating */}
        <div className="mb-2 flex items-center">
          <div className="flex text-yellow-400">
            {[...Array(5)].map((_, i) => (
              <StarIcon
                key={i}
                className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'}`}
              />
            ))}
          </div>
          <span className="ml-1 text-xs text-gray-500">({reviewCount})</span>
        </div>

        {/* Price */}
        <div className="mb-3 flex items-center">
          {discount ? (
            <>
              <span className="text-lg font-bold text-gray-900">${discountedPrice.toFixed(2)}</span>
              <span className="ml-2 text-sm text-gray-500 line-through">${price.toFixed(2)}</span>
            </>
          ) : (
            <span className="text-lg font-bold text-gray-900">${price.toFixed(2)}</span>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <button
            onClick={handleAddToCart}
            className="flex items-center rounded-lg bg-primary-600 px-3 py-2 text-sm font-medium text-white transition-colors hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <ShoppingCartIcon className="mr-1 h-4 w-4" />
            Add to Cart
          </button>
          <button
            onClick={handleToggleFavorite}
            className="rounded-full p-2 text-gray-400 transition-colors hover:text-red-500 focus:outline-none"
          >
            {isFavorite ? (
              <HeartIcon className="h-5 w-5 text-red-500" />
            ) : (
              <HeartOutlineIcon className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
