import React from 'react';

const QuantitySelector = ({ quantity, onQuantityChange }) => {
  const handleChange = (e) => {
    onQuantityChange(Number(e.target.value));
  };

  return (
    <select
      value={quantity}
      onChange={handleChange}
      className="border rounded-md p-2 shadow-sm focus:outline-none"
    >
      {[...Array(10).keys()].map((i) => (
        <option key={i} value={i + 1}>
          {i + 1}
        </option>
      ))}
    </select>
  );
};

export default QuantitySelector;
