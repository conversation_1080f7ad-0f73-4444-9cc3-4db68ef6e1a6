import React, { useState } from 'react';

const ProductGallery = ({ images }) => {
  const [selectedImage, setSelectedImage] = useState(images[0]);

  return (
    <div className="flex flex-col items-center">
      <img src={selectedImage} alt="Selected Product" className="w-full h-96 object-cover rounded-lg" />
      <div className="grid grid-cols-4 gap-2 mt-4">
        {images.map((image, index) => (
          <img
            key={index}
            src={image}
            alt={`Thumbnail ${index}`}
            className={`h-24 w-full object-cover rounded-lg cursor-pointer ${
              selectedImage === image ? 'border-2 border-primary' : ''
            }`}
            onClick={() => setSelectedImage(image)}
          />
        ))}
      </div>
    </div>
  );
};

export default ProductGallery;
