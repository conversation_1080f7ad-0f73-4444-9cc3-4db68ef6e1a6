import React, { useState } from 'react';

const ContactForm = ({ onSubmit }) => {
  const [formData, setFormData] = useState({ name: '', email: '', message: '' });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="card p-6">
      <h2 className="text-2xl font-semibold mb-4">Get in Touch</h2>
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="label">Name</label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="input w-full"
            required
          />
        </div>
        <div className="mb-4">
          <label className="label">Email</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="input w-full"
            required
          />
        </div>
        <div className="mb-4">
          <label className="label">Message</label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            className="input w-full"
            required
          />
        </div>
        <button type="submit" className="btn-primary w-full">Send Message</button>
      </form>
    </div>
  );
};

export default ContactForm;
