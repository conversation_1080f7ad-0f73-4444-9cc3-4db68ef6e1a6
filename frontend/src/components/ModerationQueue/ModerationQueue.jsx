import React from 'react';

const ModerationQueue = ({ moderationQueue, onSelectProduct }) => {
  return (
    <div>
      <h3 className="text-xl font-semibold mb-4">Moderation Queue</h3>
      <ul className="space-y-4">
        {moderationQueue.length === 0 ? (
          <p>No products in the moderation queue.</p>
        ) : (
          moderationQueue.map((product) => (
            <li
              key={product.id}
              className="bg-gray-100 p-4 rounded cursor-pointer hover:bg-gray-200"
              onClick={() => onSelectProduct(product.id)}
            >
              <p className="font-medium">{product.name}</p>
              <p className="text-sm text-gray-600">Reason: {product.moderationReason}</p>
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default ModerationQueue;
