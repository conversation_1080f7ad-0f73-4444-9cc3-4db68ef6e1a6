import React from 'react';

const PaymentMethodSelector = ({ onSelect }) => {
  const handlePaymentMethodChange = (e) => {
    onSelect(e.target.value);
  };

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Payment Method</h3>
      <select onChange={handlePaymentMethodChange} className="input-field">
        <option value="">Select Payment Method</option>
        <option value="credit_card">Credit Card</option>
        <option value="paypal">PayPal</option>
        <option value="bank_transfer">Bank Transfer</option>
      </select>
    </div>
  );
};

export default PaymentMethodSelector;
