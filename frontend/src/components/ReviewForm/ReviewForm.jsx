import React, { useState } from 'react';
import { submitReview } from '../services/api';

const ReviewForm = ({ productId }) => {
  const [review, setReview] = useState({ author: '', content: '' });

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await submitReview({ ...review, productId });
      setReview({ author: '', content: '' });
      alert('Review submitted!');
    } catch (error) {
      alert('Failed to submit review.');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white p-6 shadow-md rounded-lg mt-8">
      <h2 className="text-2xl font-semibold mb-4">Write a Review</h2>
      <input
        type="text"
        placeholder="Your Name"
        value={review.author}
        onChange={(e) => setReview({ ...review, author: e.target.value })}
        className="input w-full mb-4"
        required
      />
      <textarea
        placeholder="Your Review"
        value={review.content}
        onChange={(e) => setReview({ ...review, content: e.target.value })}
        className="input w-full mb-4"
        required
      />
      <button type="submit" className="btn-primary w-full">Submit Review</button>
    </form>
  );
};

export default ReviewForm;
