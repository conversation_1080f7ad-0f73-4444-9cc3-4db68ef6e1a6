import React from 'react';

const ProductModerationQueue = ({ moderationQueue }) => {
  const handleModerationAction = async (itemId, action) => {
    try {
      await axios.post('/api/admin/moderation_action', { itemId, action });
      alert(`Item ${action}ed successfully`);
    } catch (error) {
      console.error('Error performing moderation action:', error);
    }
  };

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Product Moderation Queue</h3>
      <table className="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th className="py-2 px-4 border">Product Name</th>
            <th className="py-2 px-4 border">Submitted By</th>
            <th className="py-2 px-4 border">Actions</th>
          </tr>
        </thead>
        <tbody>
          {moderationQueue.map((item) => (
            <tr key={item.id}>
              <td className="py-2 px-4 border">{item.productName}</td>
              <td className="py-2 px-4 border">{item.submittedBy}</td>
              <td className="py-2 px-4 border">
                <button className="text-green-500" onClick={() => handleModerationAction(item.id, 'approve')}>
                  Approve
                </button>
                <button className="text-red-500 ml-4" onClick={() => handleModerationAction(item.id, 'reject')}>
                  Reject
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProductModerationQueue;
