import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// Google Analytics Measurement ID - Replace with your actual GA ID
const GA_MEASUREMENT_ID = 'G-XXXXXXXXXX';

/**
 * GoogleAnalytics component that initializes Google Analytics and tracks page views
 * This component should be included once in your app, typically in the App component
 */
const GoogleAnalytics = () => {
  const location = useLocation();

  // Initialize Google Analytics
  useEffect(() => {
    // Check if the GA script is already loaded
    if (!window.gtag) {
      // Create script elements
      const gaScript = document.createElement('script');
      gaScript.async = true;
      gaScript.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
      
      const inlineScript = document.createElement('script');
      inlineScript.innerHTML = `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '${GA_MEASUREMENT_ID}', { send_page_view: false });
      `;
      
      // Append scripts to document
      document.head.appendChild(gaScript);
      document.head.appendChild(inlineScript);
    }
  }, []);

  // Track page views
  useEffect(() => {
    if (window.gtag) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_path: location.pathname + location.search
      });
      
      console.log(`Page view tracked: ${location.pathname + location.search}`);
    }
  }, [location]);

  return null; // This component doesn't render anything
};

/**
 * Track a custom event in Google Analytics
 * @param {string} eventName - Name of the event
 * @param {Object} eventParams - Additional parameters for the event
 */
export const trackEvent = (eventName, eventParams = {}) => {
  if (window.gtag) {
    window.gtag('event', eventName, eventParams);
    console.log(`Event tracked: ${eventName}`, eventParams);
  }
};

export default GoogleAnalytics;
