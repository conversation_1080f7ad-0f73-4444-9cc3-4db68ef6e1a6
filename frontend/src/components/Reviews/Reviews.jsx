import React, { useEffect, useState } from 'react';
import { fetchProductReviews } from '../services/api';

const Reviews = ({ productId }) => {
  const [reviews, setReviews] = useState([]);

  useEffect(() => {
    const getReviews = async () => {
      const response = await fetchProductReviews(productId);
      setReviews(response.data);
    };
    getReviews();
  }, [productId]);

  return (
    <div className="bg-white p-6 shadow-md rounded-lg mt-8">
      <h2 className="text-2xl font-semibold mb-4">Customer Reviews</h2>
      {reviews.length > 0 ? (
        <div className="grid grid-cols-1 gap-4">
          {reviews.map((review, index) => (
            <div key={index} className="card p-4">
              <h3 className="font-semibold">{review.author}</h3>
              <p>{review.content}</p>
            </div>
          ))}
        </div>
      ) : (
        <p>No reviews yet. Be the first to review this product!</p>
      )}
    </div>
  );
};

export default Reviews;
