import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

const Hero = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-to-r from-primary-600 to-primary-800">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="h-full w-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
          <path d="M0 0h32v32H0z" fill="none" />
          <circle cx="16" cy="16" r="4" fill="currentColor" />
        </svg>
      </div>

      <div className="relative mx-auto max-w-7xl px-4 py-16 sm:px-6 sm:py-24 lg:px-8">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-8">
          {/* Text Content */}
          <div className="flex flex-col justify-center">
            <h1 className="animate-slide-down text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
              Discover Amazing Products for Your Lifestyle
            </h1>
            <p className="mt-6 animate-slide-down animation-delay-200 max-w-xl text-xl text-primary-50">
              Shop our curated collection of high-quality products at competitive prices. From electronics to fashion, we've got everything you need.
            </p>
            <div className="mt-10 flex animate-slide-up animation-delay-400 items-center gap-x-6">
              <Link
                to="/products"
                className="rounded-md bg-white px-6 py-3 text-lg font-semibold text-primary-600 shadow-sm transition-all hover:bg-primary-50 hover:text-primary-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Shop Now
                <ArrowRightIcon className="ml-2 -mr-1 inline h-5 w-5" />
              </Link>
              <Link to="/contact" className="text-lg font-semibold text-white hover:text-primary-50">
                Contact Us <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>

          {/* Image */}
          <div className="flex items-center justify-center">
            <div className="relative animate-fade-in">
              <div className="absolute -inset-4 rounded-full bg-white/20 blur-xl"></div>
              <div className="relative overflow-hidden rounded-2xl bg-white/10 p-4 backdrop-blur-sm">
                <div className="aspect-[4/3] overflow-hidden rounded-xl bg-gray-200">
                  {/* Replace with your hero image */}
                  <div className="h-full w-full bg-gradient-to-br from-gray-200 to-gray-300"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
