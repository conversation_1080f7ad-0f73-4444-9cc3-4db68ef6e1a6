import React, { useState } from 'react';

const ShippingForm = ({ onShippingSubmit }) => {
  const [shippingInfo, setShippingInfo] = useState({
    fullName: '',
    address: '',
    city: '',
    postalCode: '',
  });

  const handleChange = (e) => {
    setShippingInfo({
      ...shippingInfo,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onShippingSubmit(shippingInfo);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h3 className="text-xl font-semibold">Shipping Information</h3>
      <input
        type="text"
        name="fullName"
        value={shippingInfo.fullName}
        onChange={handleChange}
        placeholder="Full Name"
        className="input-field"
      />
      <input
        type="text"
        name="address"
        value={shippingInfo.address}
        onChange={handleChange}
        placeholder="Address"
        className="input-field"
      />
      <input
        type="text"
        name="city"
        value={shippingInfo.city}
        onChange={handleChange}
        placeholder="City"
        className="input-field"
      />
      <input
        type="text"
        name="postalCode"
        value={shippingInfo.postalCode}
        onChange={handleChange}
        placeholder="Postal Code"
        className="input-field"
      />
      <button type="submit" className="w-full bg-green-600 text-white py-2 rounded-lg shadow-md">
        Save Shipping Info
      </button>
    </form>
  );
};

export default ShippingForm;
