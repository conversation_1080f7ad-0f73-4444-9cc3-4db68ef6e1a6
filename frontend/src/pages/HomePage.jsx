import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import toast from 'react-hot-toast';
import { ArrowRightIcon, ShoppingBagIcon, CreditCardIcon, TruckIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';

// Import components
import Layout from '../components/Layout/Layout.jsx';
import Hero from '../components/Hero.jsx';
import ProductCard from '../components/ProductCard.jsx';

// Import API services
import { fetchProducts, fetchCategories, fetchOffers } from '../services/api';

const HomePage = ({ userId }) => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [offers, setOffers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products
        const productsResponse = await fetchProducts();

        // Map products to the format expected by ProductCard
        const mappedProducts = productsResponse.data.results.map(product => ({
          id: product.id,
          name: product.title,
          price: parseFloat(product.price),
          rating: 4.5, // Default rating since we don't have this in the API yet
          reviewCount: 10, // Default review count
          imageSrc: product.picture,
          discount: product.discount_type !== null,
          discountPercentage: product.discount_value > 0 ? parseFloat(product.discount_value) : 0,
          category: product.category.name
        }));
        setProducts(mappedProducts);

        // Fetch categories
        const categoriesResponse = await fetchCategories();

        // Map categories with icons
        const categoryIcons = {
          'Electronics': '🖥️',
          'Clothing': '👕',
          'Home & Kitchen': '🏠',
          'Books': '📚',
          'Sports & Outdoors': '⚽'
        };

        const mappedCategories = categoriesResponse.data.results.map(category => ({
          id: category.id,
          name: category.name,
          icon: categoryIcons[category.name] || '📦',
          href: `/products?category=${category.slug}`
        }));
        setCategories(mappedCategories);

        // Fetch offers
        const offersResponse = await fetchOffers();
        setOffers(offersResponse.data.results);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading(false);
        toast.error('Failed to load data. Please try again later.');
      }
    };

    fetchData();
  }, []);

  return (
    <Layout userId={userId}>
      {/* Main Content */}
        {/* Hero Section */}
        <Hero />

        {/* Features Section */}
        <section className="py-12">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
              {/* Feature 1 */}
              <div className="rounded-xl bg-white p-6 shadow-soft transition-all duration-300 hover:shadow-hover">
                <div className="mb-4 inline-flex rounded-full bg-primary-100 p-3 text-primary-600">
                  <ShoppingBagIcon className="h-6 w-6" />
                </div>
                <h3 className="mb-2 text-lg font-semibold">Wide Selection</h3>
                <p className="text-gray-600">Thousands of products across multiple categories to choose from.</p>
              </div>

              {/* Feature 2 */}
              <div className="rounded-xl bg-white p-6 shadow-soft transition-all duration-300 hover:shadow-hover">
                <div className="mb-4 inline-flex rounded-full bg-primary-100 p-3 text-primary-600">
                  <CreditCardIcon className="h-6 w-6" />
                </div>
                <h3 className="mb-2 text-lg font-semibold">Secure Payment</h3>
                <p className="text-gray-600">Multiple secure payment options for your convenience.</p>
              </div>

              {/* Feature 3 */}
              <div className="rounded-xl bg-white p-6 shadow-soft transition-all duration-300 hover:shadow-hover">
                <div className="mb-4 inline-flex rounded-full bg-primary-100 p-3 text-primary-600">
                  <TruckIcon className="h-6 w-6" />
                </div>
                <h3 className="mb-2 text-lg font-semibold">Fast Delivery</h3>
                <p className="text-gray-600">Quick and reliable shipping to your doorstep.</p>
              </div>

              {/* Feature 4 */}
              <div className="rounded-xl bg-white p-6 shadow-soft transition-all duration-300 hover:shadow-hover">
                <div className="mb-4 inline-flex rounded-full bg-primary-100 p-3 text-primary-600">
                  <ShieldCheckIcon className="h-6 w-6" />
                </div>
                <h3 className="mb-2 text-lg font-semibold">Quality Guarantee</h3>
                <p className="text-gray-600">All products are quality checked before shipping.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Products */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="mb-12 flex items-center justify-between">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900">Featured Products</h2>
              <Link
                to="/products"
                className="flex items-center text-primary-600 hover:text-primary-700"
              >
                View all
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
            </div>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
              </div>
            ) : error ? (
              <div className="rounded-lg bg-red-50 p-4 text-center text-red-800">
                {error}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                {products.slice(0, 3).map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Categories */}
        <section className="py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <h2 className="mb-12 text-center text-3xl font-bold tracking-tight text-gray-900">Shop by Category</h2>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
              </div>
            ) : error ? (
              <div className="rounded-lg bg-red-50 p-4 text-center text-red-800">
                {error}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-6 sm:grid-cols-4">
                {categories.map((category) => (
                  <Link
                    key={category.id}
                    to={category.href}
                    className="group flex flex-col items-center rounded-xl bg-white p-6 text-center shadow-soft transition-all duration-300 hover:shadow-hover"
                  >
                    <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary-50 text-3xl transition-colors duration-300 group-hover:bg-primary-100">
                      {category.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 transition-colors duration-300 group-hover:text-primary-600">
                      {category.name}
                    </h3>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Special Offers */}
        <section className="bg-gradient-to-r from-primary-50 to-secondary-50 py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <h2 className="mb-12 text-center text-3xl font-bold tracking-tight text-gray-900">Special Offers</h2>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
              </div>
            ) : error ? (
              <div className="rounded-lg bg-red-50 p-4 text-center text-red-800">
                {error}
              </div>
            ) : offers.length > 0 ? (
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                {offers.slice(0, 2).map((offer, index) => (
                  <div
                    key={offer.id}
                    className={`overflow-hidden rounded-xl shadow-lg ${
                      index % 2 === 0
                        ? 'bg-gradient-to-r from-yellow-500 to-amber-500'
                        : 'bg-gradient-to-r from-primary-500 to-primary-700'
                    }`}
                  >
                    <div className="p-8 text-white">
                      <h3 className="mb-2 text-2xl font-bold">{offer.name}</h3>
                      <p className="mb-6 text-lg">
                        {offer.offer_type === 'percentage'
                          ? `Get ${offer.discount_value}% off!`
                          : `Get $${offer.discount_value} off!`}
                      </p>
                      <Link
                        to={`/products/${offer.product.id}`}
                        className={`inline-block rounded-lg bg-white px-5 py-3 font-semibold transition-colors ${
                          index % 2 === 0
                            ? 'text-amber-600 hover:bg-amber-50'
                            : 'text-primary-600 hover:bg-primary-50'
                        }`}
                      >
                        Shop Now
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                {/* Default offer when no offers from API */}
                <div className="overflow-hidden rounded-xl bg-gradient-to-r from-yellow-500 to-amber-500 shadow-lg">
                  <div className="p-8 text-white">
                    <h3 className="mb-2 text-2xl font-bold">Special Sale</h3>
                    <p className="mb-6 text-lg">Get up to 15% off on selected items!</p>
                    <Link
                      to="/products?discount=true"
                      className="inline-block rounded-lg bg-white px-5 py-3 font-semibold text-amber-600 transition-colors hover:bg-amber-50"
                    >
                      Shop Now
                    </Link>
                  </div>
                </div>

                {/* Default second offer */}
                <div className="overflow-hidden rounded-xl bg-gradient-to-r from-primary-500 to-primary-700 shadow-lg">
                  <div className="p-8 text-white">
                    <h3 className="mb-2 text-2xl font-bold">Free Shipping</h3>
                    <p className="mb-6 text-lg">On all orders over $50. Limited time offer!</p>
                    <Link
                      to="/products"
                      className="inline-block rounded-lg bg-white px-5 py-3 font-semibold text-primary-600 transition-colors hover:bg-primary-50"
                    >
                      Learn More
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Newsletter */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="rounded-2xl bg-primary-700 px-6 py-12 sm:px-12 sm:py-16">
              <div className="mx-auto max-w-3xl text-center">
                <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                  Stay updated with our newsletter
                </h2>
                <p className="mx-auto mt-4 max-w-xl text-lg text-primary-100">
                  Get the latest product updates, offers, and news delivered straight to your inbox.
                </p>
                <form className="mt-8 sm:mx-auto sm:max-w-md">
                  <div className="flex">
                    <label htmlFor="email-address" className="sr-only">
                      Email address
                    </label>
                    <input
                      id="email-address"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      className="w-full min-w-0 rounded-l-md border-0 px-4 py-3 text-base text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                      placeholder="Enter your email"
                    />
                    <button
                      type="submit"
                      className="flex-shrink-0 rounded-r-md bg-primary-900 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    >
                      Subscribe
                    </button>
                  </div>
                  <p className="mt-3 text-sm text-primary-200">
                    We care about your data. Read our{' '}
                    <Link to="/privacy" className="font-medium text-white underline">
                      Privacy Policy
                    </Link>
                    .
                  </p>
                </form>
              </div>
            </div>
          </div>
        </section>
    </Layout>
  );
};

export default HomePage;
