/**
 * Main App component for the eCommerce platform
 *
 * This component sets up the routing structure and provides:
 * - Analytics tracking via Google Analytics
 * - Toast notifications for user feedback
 * - Context providers for shared state
 */
import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import toast from 'react-hot-toast';
import GoogleAnalytics from './components/Analytics/GoogleAnalytics';
import { AnalyticsProvider } from './contexts/AnalyticsContext';

// Page components
import HomePage from './pages/HomePage.jsx';
import ProductListingPage from './pages/ProductListingPage.jsx';
import ProductDetailPage from './pages/ProductDetailPage.jsx';
import CartPage from './pages/CartPage.jsx';
import CheckoutPage from './pages/CheckoutPage.jsx';
import OrderConfirmationPage from './pages/OrderConfirmationPage.jsx';
import LoginPage from './pages/LoginPage.jsx';
import RegistrationPage from './pages/RegistrationPage.jsx';
import UserProfilePage from './pages/UserProfilePage.jsx';
import ContactUsPage from './pages/ContactUsPage.jsx';
import FAQPage from './pages/FAQPage.jsx';
import AdminDashboardPage from './pages/AdminDashboardPage.jsx';
import ProductModerationPage from './pages/ProductModerationPage.jsx';
import ForgotPasswordPage from './pages/ForgotPasswordPage.jsx';
import ResetPasswordPage from './pages/ResetPasswordPage.jsx';
import OrderManagementPage from './pages/OrderManagementPage.jsx';
import AnalyticsDashboardPage from './pages/AnalyticsDashboardPage.jsx';

// Import API test functions
import { runAllTests } from './test-api-connection';

// Import CSS for animations
import './assets/styles/animations.css';

function App() {
  // Mock user ID for demonstration purposes
  const [userId, setUserId] = useState(null); // Set to a value to simulate logged-in user
  const [isLoading, setIsLoading] = useState(true);

  // Initialize app and test API connection
  useEffect(() => {
    // Simulate app initialization
    const timer = setTimeout(() => {
      setIsLoading(false);

      // Run API tests to verify backend connection
      runAllTests()
        .then(success => {
          if (success) {
            toast.success('Backend API connection successful!');
          } else {
            toast.error('Failed to connect to backend API. Check console for details.');
          }
        })
        .catch(error => {
          console.error('Error running API tests:', error);
          toast.error('Error testing backend connection. Check console for details.');
        });
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Loading screen
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mb-4 h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading...</h2>
        </div>
      </div>
    );
  }

  return (
    <Router>
      {/* Global toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            style: {
              background: '#059669',
              color: '#fff',
            },
          },
          error: {
            duration: 4000,
            style: {
              background: '#DC2626',
              color: '#fff',
            },
          },
        }}
      />

      {/* Google Analytics tracking */}
      <GoogleAnalytics />

      {/* Analytics Provider for tracking user behavior */}
      <AnalyticsProvider>
        <Routes>
          <Route path="/" element={<HomePage userId={userId} />} />
          <Route path="/products" element={<ProductListingPage userId={userId} />} />
          <Route path="/products/:productId" element={<ProductDetailPage userId={userId} />} />
          <Route path="/cart" element={<CartPage userId={userId} />} />
          <Route path="/checkout" element={<CheckoutPage userId={userId} />} />
          <Route path="/order-confirmation" element={<OrderConfirmationPage userId={userId} />} />
          <Route path="/login" element={<LoginPage setUserId={setUserId} />} />
          <Route path="/register" element={<RegistrationPage setUserId={setUserId} />} />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/reset-password" element={<ResetPasswordPage />} />
          <Route path="/profile" element={<UserProfilePage userId={userId} />} />
          <Route path="/contact" element={<ContactUsPage userId={userId} />} />
          <Route path="/faq" element={<FAQPage userId={userId} />} />
          <Route path="/admin" element={<AdminDashboardPage userId={userId} />} />
          <Route path="/admin/moderation" element={<ProductModerationPage userId={userId} />} />
          <Route path="/admin/orders" element={<OrderManagementPage userId={userId} />} />
          <Route path="/analytics" element={<AnalyticsDashboardPage userId={userId} />} />
        </Routes>
      </AnalyticsProvider>
    </Router>
  );
}

export default App;
