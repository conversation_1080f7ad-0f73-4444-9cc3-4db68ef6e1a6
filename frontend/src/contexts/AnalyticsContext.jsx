import React, { createContext, useContext, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { trackEvent } from '../components/Analytics/GoogleAnalytics';
import { useAuth } from './AuthContext';
import { trackUserBehavior } from '../services/analyticsService';

// Create context
const AnalyticsContext = createContext();

/**
 * Analytics Provider component that manages tracking user behavior
 * and sending data to both Google Analytics and our backend
 */
export const AnalyticsProvider = ({ children }) => {
  const location = useLocation();
  const { currentUser } = useAuth();

  // Track page views
  useEffect(() => {
    const pageName = getPageNameFromPath(location.pathname);
    
    // Track page view in our backend if user is logged in
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action: 'page_view',
        metadata: {
          page: pageName,
          path: location.pathname,
          referrer: document.referrer,
          timestamp: new Date().toISOString()
        }
      });
    }
  }, [location, currentUser]);

  /**
   * Track a user action/event
   * @param {string} action - The action type (e.g., 'click', 'add_to_cart')
   * @param {Object} metadata - Additional data about the action
   * @param {string} productId - Optional product ID if action is related to a product
   */
  const trackAction = (action, metadata = {}, productId = null) => {
    // Track in Google Analytics
    trackEvent(action, metadata);
    
    // Track in our backend if user is logged in
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action,
        productId,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  // Helper function to get page name from path
  const getPageNameFromPath = (path) => {
    if (path === '/') return 'home';
    
    // Remove leading slash and split by remaining slashes
    const segments = path.substring(1).split('/');
    
    if (segments.length === 1) return segments[0];
    
    // Handle product detail pages
    if (segments[0] === 'products' && segments.length > 1) {
      return 'product_detail';
    }
    
    return segments.join('_');
  };

  return (
    <AnalyticsContext.Provider value={{ trackAction }}>
      {children}
    </AnalyticsContext.Provider>
  );
};

// Custom hook to use analytics
export const useAnalytics = () => {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
};

export default AnalyticsContext;
