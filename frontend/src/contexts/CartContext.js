import React, { createContext, useState, useEffect, useContext } from 'react';
import { cartService } from '../services/api';
import { useAuth } from './AuthContext';

const CartContext = createContext();

export const useCart = () => useContext(CartContext);

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { currentUser } = useAuth();

  useEffect(() => {
    if (currentUser) {
      fetchCart();
    } else {
      setCart(null);
    }
  }, [currentUser]);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const response = await cartService.getCart();
      setCart(response.data.length > 0 ? response.data[0] : null);
      setError(null);
    } catch (err) {
      setError('Failed to fetch cart');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId, quantity = 1) => {
    try {
      setLoading(true);
      
      // If no cart exists, create one first
      if (!cart) {
        const cartResponse = await cartService.getCart();
        if (cartResponse.data.length === 0) {
          // Create a new cart
          const newCartResponse = await cartService.createCart();
          setCart(newCartResponse.data);
          
          // Add item to the new cart
          const response = await cartService.addToCart(newCartResponse.data.id, productId, quantity);
          setCart(response.data);
        } else {
          setCart(cartResponse.data[0]);
          
          // Add item to the existing cart
          const response = await cartService.addToCart(cartResponse.data[0].id, productId, quantity);
          setCart(response.data);
        }
      } else {
        // Add item to the existing cart
        const response = await cartService.addToCart(cart.id, productId, quantity);
        setCart(response.data);
      }
      
      setError(null);
    } catch (err) {
      setError('Failed to add item to cart');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (itemId) => {
    if (!cart) return;
    
    try {
      setLoading(true);
      const response = await cartService.removeFromCart(cart.id, itemId);
      setCart(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to remove item from cart');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const updateCartItem = async (itemId, quantity) => {
    if (!cart) return;
    
    try {
      setLoading(true);
      const response = await cartService.updateCartItem(cart.id, itemId, quantity);
      setCart(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to update cart item');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const checkout = async () => {
    if (!cart) return;
    
    try {
      setLoading(true);
      const response = await cartService.checkout(cart.id);
      setCart(null);
      setError(null);
      return response.data;
    } catch (err) {
      setError('Checkout failed');
      console.error(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    cart,
    loading,
    error,
    fetchCart,
    addToCart,
    removeFromCart,
    updateCartItem,
    checkout,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

export default CartContext;
