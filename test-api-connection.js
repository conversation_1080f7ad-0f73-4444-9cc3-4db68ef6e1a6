const axios = require('axios');

// Base API URL
const API_BASE_URL = 'http://localhost:8000/api';

// Test function to fetch products
const testFetchProducts = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/products/`);
    console.log('Products fetched successfully:');
    console.log(`Found ${response.data.count} products`);
    return true;
  } catch (error) {
    console.error('Error fetching products:', error.message);
    return false;
  }
};

// Test function to fetch categories
const testFetchCategories = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/categories/`);
    console.log('Categories fetched successfully:');
    console.log(`Found ${response.data.count} categories`);
    return true;
  } catch (error) {
    console.error('Error fetching categories:', error.message);
    return false;
  }
};

// Test function to fetch offers
const testFetchOffers = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/offers/`);
    console.log('Offers fetched successfully:');
    console.log(`Found ${response.data.count} offers`);
    return true;
  } catch (error) {
    console.error('Error fetching offers:', error.message);
    return false;
  }
};

// Run all tests
const runAllTests = async () => {
  console.log('Running API tests...');
  
  // Test products API
  const productsResult = await testFetchProducts();
  
  // Test categories API
  const categoriesResult = await testFetchCategories();
  
  // Test offers API
  const offersResult = await testFetchOffers();
  
  // Check if all tests passed
  if (productsResult && categoriesResult && offersResult) {
    console.log('\n✅ All API tests passed! Frontend and backend are properly connected.');
    return true;
  } else {
    console.error('\n❌ Some API tests failed. Check the errors above.');
    return false;
  }
};

// Run the tests
runAllTests()
  .then(success => {
    if (!success) {
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Error running tests:', error);
    process.exit(1);
  });
