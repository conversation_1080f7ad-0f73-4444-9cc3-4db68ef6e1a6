#!/bin/bash

# Navigate to the pages directory
cd frontend/src/pages

# Rename all .js files to .jsx
for file in *.js; do
  # Skip files that already have a .jsx counterpart
  if [ -f "${file%.js}.jsx" ]; then
    echo "Skipping $file as ${file%.js}.jsx already exists"
  else
    # Rename the file
    mv "$file" "${file%.js}.jsx"
    echo "Renamed $file to ${file%.js}.jsx"
  fi
done

# Navigate to the components directory
cd ../components

# Rename all .js files to .jsx
for file in *.js; do
  # Skip files that already have a .jsx counterpart
  if [ -f "${file%.js}.jsx" ]; then
    echo "Skipping $file as ${file%.js}.jsx already exists"
  else
    # Rename the file
    mv "$file" "${file%.js}.jsx"
    echo "Renamed $file to ${file%.js}.jsx"
  fi
done

echo "Renaming complete!"
