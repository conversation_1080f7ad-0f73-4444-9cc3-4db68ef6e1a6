#!/usr/bin/env python
import os
import sys
import django
import MySQLdb

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce_backend.settings')
django.setup()

from django.conf import settings

def test_mysql_connection():
    """Test the MySQL database connection."""
    try:
        # Get database settings from Django settings
        db_settings = settings.DATABASES['default']
        
        # Connect to the database
        connection = MySQLdb.connect(
            host=db_settings['HOST'],
            user=db_settings['USER'],
            passwd=db_settings['PASSWORD'],
            db=db_settings['NAME'],
            port=int(db_settings['PORT']),
        )
        
        # Create a cursor
        cursor = connection.cursor()
        
        # Execute a simple query
        cursor.execute("SELECT VERSION()")
        
        # Fetch the result
        version = cursor.fetchone()
        
        # Close the cursor and connection
        cursor.close()
        connection.close()
        
        print(f"Successfully connected to MySQL database!")
        print(f"MySQL version: {version[0]}")
        return True
    
    except Exception as e:
        print(f"Error connecting to MySQL database: {e}")
        return False

if __name__ == '__main__':
    test_mysql_connection()
