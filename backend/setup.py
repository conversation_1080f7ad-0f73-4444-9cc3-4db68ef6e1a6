#!/usr/bin/env python
import os
import sys
import django
from django.contrib.auth import get_user_model

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce_backend.settings')
django.setup()

# Import models
from api.models import (
    User, Role, Category, Product, Offer, 
    Cart, CartItem, Order, OrderLine, Review
)

def create_superuser():
    User = get_user_model()
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_active=True,
            is_staff=True
        )
        print("Superuser created successfully!")
    else:
        print("Superuser already exists.")

def create_roles():
    roles = ['Admin', 'Customer', 'Moderator']
    for role_name in roles:
        Role.objects.get_or_create(
            name=role_name,
            defaults={'description': f'{role_name} role'}
        )
    print("Roles created successfully!")

def create_categories():
    categories = [
        {'name': 'Electronics', 'description': 'Electronic devices and gadgets'},
        {'name': 'Clothing', 'description': 'Apparel and fashion items'},
        {'name': 'Books', 'description': 'Books and publications'},
        {'name': 'Home & Kitchen', 'description': 'Home and kitchen appliances'},
        {'name': 'Sports & Outdoors', 'description': 'Sports equipment and outdoor gear'},
    ]
    
    for category_data in categories:
        Category.objects.get_or_create(
            name=category_data['name'],
            defaults={
                'description': category_data['description'],
                'tags': [category_data['name'].lower()]
            }
        )
    print("Categories created successfully!")

def create_products():
    # Ensure categories exist
    electronics = Category.objects.get(name='Electronics')
    clothing = Category.objects.get(name='Clothing')
    books = Category.objects.get(name='Books')
    
    products = [
        {
            'title': 'Smartphone X', 
            'category': electronics,
            'summary': 'Latest smartphone with advanced features',
            'description': 'A powerful smartphone with a high-resolution camera, fast processor, and long battery life.',
            'price': 699.99,
            'stock_quantity': 50,
            'tags': ['smartphone', 'electronics', 'mobile']
        },
        {
            'title': 'Laptop Pro', 
            'category': electronics,
            'summary': 'Professional laptop for work and entertainment',
            'description': 'High-performance laptop with a stunning display, fast SSD storage, and powerful processor.',
            'price': 1299.99,
            'stock_quantity': 30,
            'tags': ['laptop', 'electronics', 'computer']
        },
        {
            'title': 'Casual T-Shirt', 
            'category': clothing,
            'summary': 'Comfortable cotton t-shirt',
            'description': 'Soft, breathable cotton t-shirt perfect for everyday wear.',
            'price': 19.99,
            'stock_quantity': 100,
            'tags': ['t-shirt', 'clothing', 'casual']
        },
        {
            'title': 'Python Programming', 
            'category': books,
            'summary': 'Comprehensive guide to Python programming',
            'description': 'Learn Python programming from basics to advanced concepts with practical examples.',
            'price': 39.99,
            'stock_quantity': 75,
            'tags': ['python', 'programming', 'book']
        },
    ]
    
    for product_data in products:
        Product.objects.get_or_create(
            title=product_data['title'],
            defaults={
                'category': product_data['category'],
                'summary': product_data['summary'],
                'description': product_data['description'],
                'price': product_data['price'],
                'stock_quantity': product_data['stock_quantity'],
                'tags': product_data['tags'],
                'is_active': True
            }
        )
    print("Products created successfully!")

def create_offers():
    products = Product.objects.all()
    
    for i, product in enumerate(products):
        if i % 2 == 0:  # Create offers for every other product
            Offer.objects.get_or_create(
                product=product,
                name=f'Special Discount on {product.title}',
                defaults={
                    'offer_type': 'percentage',
                    'discount_value': 15.00,
                    'start_date': '2023-01-01 00:00:00',
                    'end_date': '2023-12-31 23:59:59',
                    'is_active': True
                }
            )
    print("Offers created successfully!")

def main():
    create_superuser()
    create_roles()
    create_categories()
    create_products()
    create_offers()
    print("Setup completed successfully!")

if __name__ == '__main__':
    main()
