"""
Models for the eCommerce platform.
This module defines all database models used in the application.
"""
import uuid
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.text import slugify
from django.core.exceptions import ValidationError

class User(AbstractUser):
    """
    Extended User model with additional fields for eCommerce functionality.
    Inherits from Django's AbstractUser and adds custom fields.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.SlugField(unique=True, blank=True)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    locale = models.CharField(max_length=10, default='en-US')
    bio = models.TextField(blank=True, null=True)
    company = models.CharField(max_length=255, blank=True, null=True)
    email_validated = models.BooleanField(default=False)
    phone_validated = models.BooleanField(default=False)
    reset_token = models.CharField(max_length=100, blank=True, null=True)
    last_login = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Add related_name to avoid clashes with auth.User
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='api_user_set',
        related_query_name='api_user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='api_user_set',
        related_query_name='api_user',
    )

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.username)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.username

class Role(models.Model):
    """
    Role model for user permissions.
    Defines different roles like 'admin', 'manager', 'customer', etc.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

class UserRole(models.Model):
    """
    Many-to-many relationship between Users and Roles.
    Allows assigning multiple roles to users with timestamps.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles')
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'role')

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"

class Credential(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='credentials')
    provider_id = models.CharField(max_length=255)
    provider_key = models.UUIDField(default=uuid.uuid4)
    password_hash = models.CharField(max_length=255, blank=True, null=True)
    password_salt = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} - {self.provider_id}"

class SocialProfile(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='social_profiles')
    platform = models.CharField(max_length=100)
    platform_user = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'platform')

    def __str__(self):
        return f"{self.user.username} - {self.platform}"

class Category(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent_category = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='subcategories')
    slug = models.SlugField(unique=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    tags = models.JSONField(default=list, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

class Product(models.Model):
    """
    Product model representing items for sale in the eCommerce platform.
    Includes inventory tracking, pricing, and categorization.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    title = models.CharField(max_length=255)
    slug = models.SlugField(unique=True, blank=True)
    summary = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    picture = models.ImageField(upload_to='products/', blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_type = models.CharField(max_length=20, blank=True, null=True)  # percentage, fixed, etc.
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tags = models.JSONField(default=list, blank=True)
    stock_quantity = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    @property
    def all_images(self):
        """Return a list of all product images including the main picture"""
        images = []
        if self.picture:
            images.append(self.picture.url)

        # Add additional images
        additional_images = self.additional_images.all().order_by('order')
        for img in additional_images:
            if img.image:
                images.append(img.image.url)

        return images

    def update_inventory(self, quantity_change, change_type, user, reference_order=None, notes=None):
        """
        Update product inventory with proper tracking

        Args:
            quantity_change: Integer, positive for additions, negative for subtractions
            change_type: String, type of inventory change (see InventoryChange.CHANGE_TYPE_CHOICES)
            user: User object, the user making the change
            reference_order: Optional Order object, if change is related to an order
            notes: Optional string, additional notes about the change

        Returns:
            InventoryChange object if successful

        Raises:
            ValidationError if resulting inventory would be negative
        """
        previous_quantity = self.stock_quantity
        new_quantity = previous_quantity + quantity_change

        if new_quantity < 0:
            raise ValidationError("Cannot reduce inventory below zero.")

        # Update the product's stock quantity
        self.stock_quantity = new_quantity
        self.save(update_fields=['stock_quantity', 'updated_at'])

        # Create inventory change record
        from .models import InventoryChange  # Import here to avoid circular import
        inventory_change = InventoryChange.objects.create(
            product=self,
            quantity_change=quantity_change,
            change_type=change_type,
            previous_quantity=previous_quantity,
            new_quantity=new_quantity,
            changed_by=user,
            reference_order=reference_order,
            notes=notes
        )

        return inventory_change

    def add_inventory(self, quantity, user, notes=None):
        """Add inventory to the product"""
        if quantity <= 0:
            raise ValidationError("Quantity to add must be positive")
        return self.update_inventory(quantity, 'add', user, notes=notes)

    def subtract_inventory(self, quantity, user, notes=None):
        """Subtract inventory from the product"""
        if quantity <= 0:
            raise ValidationError("Quantity to subtract must be positive")
        return self.update_inventory(-quantity, 'subtract', user, notes=notes)

    def order_inventory(self, quantity, user, order, notes=None):
        """Subtract inventory due to an order"""
        if quantity <= 0:
            raise ValidationError("Order quantity must be positive")
        return self.update_inventory(-quantity, 'order', user, reference_order=order, notes=notes)

    def return_inventory(self, quantity, user, order, notes=None):
        """Add inventory due to an order return/cancellation"""
        if quantity <= 0:
            raise ValidationError("Return quantity must be positive")
        return self.update_inventory(quantity, 'return', user, reference_order=order, notes=notes)

    def __str__(self):
        return self.title


class ProductImage(models.Model):
    """Model to store additional product images (up to 4 additional images)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='additional_images')
    image = models.ImageField(upload_to='products/', blank=True, null=True)
    order = models.PositiveSmallIntegerField(default=0)
    alt_text = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order']
        # Ensure we can only have 4 additional images per product (5 total with the main picture)
        constraints = [
            models.UniqueConstraint(fields=['product', 'order'], name='unique_product_image_order')
        ]

    def __str__(self):
        return f"{self.product.title} - Image {self.order + 1}"

class ProductModerationLog(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='moderation_logs')
    moderator = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=100)
    comments = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product.title} - {self.action} by {self.moderator.username}"

class Cart(models.Model):
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('abandoned', 'Abandoned'),
        ('converted', 'Converted to Order'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='carts')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Cart {self.id} - {self.created_by.username}"

class CartItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    quantity = models.IntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product.title} ({self.quantity}) in Cart {self.cart.id}"

class Order(models.Model):
    PAYMENT_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    )

    DELIVERY_STATUS_CHOICES = (
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders')
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    delivery_status = models.CharField(max_length=20, choices=DELIVERY_STATUS_CHOICES, default='processing')
    stripe_payment_intent_id = models.CharField(max_length=255, blank=True, null=True)
    stripe_payment_method_id = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Order {self.id} - {self.user.username}"

class OrderLine(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='order_lines')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    quantity = models.IntegerField(default=1)

    def __str__(self):
        return f"{self.product.title} ({self.quantity}) in Order {self.order.id}"

class Review(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reviews')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='reviews')
    rating = models.DecimalField(max_digits=3, decimal_places=1)
    comment = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'product')

    def __str__(self):
        return f"{self.product.title} - {self.rating} by {self.user.username}"

class PersonalizedRecommendation(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommendations')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    recommendation_score = models.FloatField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'product')

    def __str__(self):
        return f"{self.product.title} for {self.user.username} - Score: {self.recommendation_score}"

class Offer(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='offers')
    name = models.CharField(max_length=255)
    offer_type = models.CharField(max_length=20)  # e.g., "percentage", "flat", "bundle"
    discount_value = models.DecimalField(max_digits=10, decimal_places=2)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_festival = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} - {self.product.title}"

class UserBehavior(models.Model):
    ACTION_CHOICES = (
        ('search', 'Search'),
        ('view', 'View'),
        ('click', 'Click'),
        ('add_to_cart', 'Add to Cart'),
        ('purchase', 'Purchase'),
        ('page_view', 'Page View'),
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('register', 'Register'),
        ('wishlist_add', 'Add to Wishlist'),
        ('review', 'Write Review'),
        ('share', 'Share Product'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='behaviors')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='user_behaviors', null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    action_time = models.DateTimeField(auto_now_add=True)
    session_id = models.CharField(max_length=100, blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    referrer = models.URLField(blank=True, null=True)
    page = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        product_title = self.product.title if self.product else "N/A"
        return f"{self.user.username} - {self.action} - {product_title}"


class StripePayment(models.Model):
    """Model to store Stripe payment information"""
    PAYMENT_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('succeeded', 'Succeeded'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
        ('canceled', 'Canceled'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='stripe_payments')
    payment_intent_id = models.CharField(max_length=255)
    payment_method_id = models.CharField(max_length=255, blank=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"Payment {self.payment_intent_id} for Order {self.order.id}"


class InventoryChange(models.Model):
    """Model to track inventory changes for products"""
    CHANGE_TYPE_CHOICES = (
        ('add', 'Addition'),
        ('subtract', 'Subtraction'),
        ('order', 'Order Placement'),
        ('return', 'Order Return'),
        ('adjustment', 'Manual Adjustment'),
        ('initial', 'Initial Stock'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='inventory_changes')
    quantity_change = models.IntegerField()  # Positive for additions, negative for subtractions
    change_type = models.CharField(max_length=20, choices=CHANGE_TYPE_CHOICES)
    previous_quantity = models.IntegerField()
    new_quantity = models.IntegerField()
    changed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='inventory_changes')
    reference_order = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True, blank=True, related_name='inventory_changes')
    notes = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def clean(self):
        """Validate that new_quantity is not negative"""
        if self.new_quantity < 0:
            raise ValidationError("Inventory cannot be negative.")

        # Ensure quantity_change matches the difference between new and previous
        if self.new_quantity - self.previous_quantity != self.quantity_change:
            raise ValidationError("Quantity change must equal the difference between new and previous quantities.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.product.title} - {self.change_type} - {self.quantity_change} units"
