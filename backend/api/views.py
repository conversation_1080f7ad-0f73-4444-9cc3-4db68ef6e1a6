"""
API views for the eCommerce platform.

This module contains all the view classes that handle API requests,
including authentication, product management, cart operations,
order processing, and inventory management.
"""
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.shortcuts import get_object_or_404
from django.db.models import Avg, Sum, F, Q
from django.contrib.auth import authenticate
from django.core.mail import send_mail
from django.conf import settings
from django.core.exceptions import ValidationError
import uuid
import random
import string
import json
from .permissions import IsManager, IsManagerOrReadOnly
from .stripe_utils import (
    create_payment_intent, retrieve_payment_intent,
    confirm_payment_intent, cancel_payment_intent, construct_event
)

from .models import (
    User, Role, UserRole, Category, Product, ProductImage, ProductModerationLog,
    Cart, CartItem, Order, OrderLine, Review, Offer, UserBehavior,
    InventoryChange, StripePayment
)
from .serializers import (
    UserSerializer, RoleSerializer, UserRoleSerializer,
    CategorySerializer, ProductSerializer, ProductImageSerializer, ProductModerationLogSerializer,
    CartSerializer, CartItemSerializer, OrderSerializer, OrderLineSerializer,
    ReviewSerializer, OfferSerializer, UserBehaviorSerializer,
    UserRegistrationSerializer, InventoryChangeSerializer, StripePaymentSerializer
)

# Authentication Views
class RegisterView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)
            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class LoginView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        username = request.data.get('username')
        password = request.data.get('password')

        user = authenticate(username=username, password=password)
        if user:
            refresh = RefreshToken.for_user(user)
            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': UserSerializer(user).data
            })
        return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

class ForgotPasswordView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)

            # Generate a random reset token
            reset_token = ''.join(random.choices(string.ascii_letters + string.digits, k=32))

            # Store the reset token (in a real app, you'd store this securely with an expiration time)
            # For this example, we'll just update a field on the user model
            user.reset_token = reset_token
            user.save()

            # Send email with reset link (in a real app, you'd use a proper email template)
            reset_link = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"
            email_body = f"Click the following link to reset your password: {reset_link}"

            # In a real app, you'd configure email settings and actually send the email
            # For this example, we'll just log the email content
            print(f"Would send email to {email} with reset link: {reset_link}")

            # Uncomment this to actually send the email if email settings are configured
            # send_mail(
            #     'Password Reset Request',
            #     email_body,
            #     settings.DEFAULT_FROM_EMAIL,
            #     [email],
            #     fail_silently=False,
            # )

            return Response({'message': 'Password reset instructions sent to your email'})

        except User.DoesNotExist:
            # For security reasons, don't reveal that the email doesn't exist
            return Response({'message': 'Password reset instructions sent to your email'})

class ResetPasswordView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        token = request.data.get('token')
        new_password = request.data.get('new_password')

        if not token or not new_password:
            return Response({'error': 'Token and new password are required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(reset_token=token)

            # Set the new password
            user.set_password(new_password)

            # Clear the reset token
            user.reset_token = None
            user.save()

            return Response({'message': 'Password has been reset successfully'})

        except User.DoesNotExist:
            return Response({'error': 'Invalid or expired token'}, status=status.HTTP_400_BAD_REQUEST)

# User Views
class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['username', 'email', 'first_name', 'last_name']

    def get_permissions(self):
        if self.action == 'create':
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

# Role Views
class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAdminUser]

# Category Views
class CategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'description']

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        category = self.get_object()
        products = Product.objects.filter(category=category, is_active=True)
        serializer = ProductSerializer(products, many=True)
        return Response(serializer.data)

# Product Views
class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter]
    search_fields = ['title', 'description', 'tags']

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsManager()]  # Allow both managers and admins
        return super().get_permissions()

    @action(detail=True, methods=['get'])
    def reviews(self, request, pk=None):
        product = self.get_object()
        reviews = Review.objects.filter(product=product)
        serializer = ReviewSerializer(reviews, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def rating(self, request, pk=None):
        product = self.get_object()
        avg_rating = Review.objects.filter(product=product).aggregate(Avg('rating'))
        return Response({'average_rating': avg_rating['rating__avg'] or 0})

# Product Image Views
class ProductImageViewSet(viewsets.ModelViewSet):
    """ViewSet for managing product images"""
    queryset = ProductImage.objects.all()
    serializer_class = ProductImageSerializer
    permission_classes = [IsManager]  # Allow both managers and admins

    def get_queryset(self):
        """Filter images by product if product_id is provided"""
        queryset = super().get_queryset()
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product__id=product_id)
        return queryset

    def perform_create(self, serializer):
        """Set the product when creating a new image"""
        product_id = self.request.data.get('product_id')
        product = get_object_or_404(Product, id=product_id)
        serializer.save(product=product)

# Product Moderation Views
class ProductModerationLogViewSet(viewsets.ModelViewSet):
    queryset = ProductModerationLog.objects.all()
    serializer_class = ProductModerationLogSerializer
    permission_classes = [IsManager]  # Allow both managers and admins

    @action(detail=False, methods=['post'])
    def moderate(self, request):
        product_id = request.data.get('product_id')
        action = request.data.get('action')
        comments = request.data.get('comments', '')

        product = get_object_or_404(Product, id=product_id)

        log = ProductModerationLog.objects.create(
            product=product,
            moderator=request.user,
            action=action,
            comments=comments
        )

        # Update product status based on action
        if action == 'approve':
            product.is_active = True
        elif action == 'reject':
            product.is_active = False
        product.save()

        serializer = self.get_serializer(log)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

# Cart Views
class CartViewSet(viewsets.ModelViewSet):
    serializer_class = CartSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Cart.objects.filter(created_by=self.request.user)

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def add_item(self, request, pk=None):
        cart = self.get_object()
        product_id = request.data.get('product_id')
        quantity = int(request.data.get('quantity', 1))

        product = get_object_or_404(Product, id=product_id)

        # Check if item already exists in cart
        try:
            cart_item = CartItem.objects.get(cart=cart, product=product)
            cart_item.quantity += quantity
            cart_item.save()
        except CartItem.DoesNotExist:
            CartItem.objects.create(
                cart=cart,
                product=product,
                price=product.price,
                quantity=quantity
            )

        serializer = self.get_serializer(cart)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def remove_item(self, request, pk=None):
        cart = self.get_object()
        item_id = request.data.get('item_id')

        item = get_object_or_404(CartItem, id=item_id, cart=cart)
        item.delete()

        serializer = self.get_serializer(cart)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_item(self, request, pk=None):
        cart = self.get_object()
        item_id = request.data.get('item_id')
        quantity = int(request.data.get('quantity', 1))

        item = get_object_or_404(CartItem, id=item_id, cart=cart)
        item.quantity = quantity
        item.save()

        serializer = self.get_serializer(cart)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def checkout(self, request, pk=None):
        cart = self.get_object()

        # Calculate total price
        total_price = sum(item.price * item.quantity for item in cart.items.all())

        # Create order
        order = Order.objects.create(
            user=request.user,
            total_price=total_price,
            payment_status='pending',
            delivery_status='processing'
        )

        # Create order lines and update inventory
        for item in cart.items.all():
            OrderLine.objects.create(
                order=order,
                product=item.product,
                price=item.price,
                quantity=item.quantity
            )

            # Update inventory - subtract the ordered quantity
            try:
                item.product.order_inventory(
                    quantity=item.quantity,
                    user=request.user,
                    order=order,
                    notes=f"Order {order.id} - Automatic inventory reduction"
                )
            except ValidationError as e:
                # If inventory update fails, log the error but continue with order
                # In a real app, you might want to handle this differently
                print(f"Inventory update error for product {item.product.id}: {str(e)}")

        # Update cart status
        cart.status = 'converted'
        cart.save()

        return Response({
            'order_id': order.id,
            'message': 'Order created successfully'
        }, status=status.HTTP_201_CREATED)

# Order Views
class OrderViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        order = self.get_object()

        if order.delivery_status in ['delivered', 'cancelled']:
            return Response({'error': 'Cannot cancel this order'}, status=status.HTTP_400_BAD_REQUEST)

        # Return inventory for each order line
        for order_line in order.order_lines.all():
            try:
                order_line.product.return_inventory(
                    quantity=order_line.quantity,
                    user=request.user,
                    order=order,
                    notes=f"Order {order.id} cancelled - Inventory returned"
                )
            except ValidationError as e:
                # Log error but continue with cancellation
                print(f"Inventory return error for product {order_line.product.id}: {str(e)}")

        order.delivery_status = 'cancelled'
        order.save()

        serializer = self.get_serializer(order)
        return Response(serializer.data)

# Admin Order Views
class AdminOrderViewSet(viewsets.ModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [IsManager]  # Allow both managers and admins

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        order = self.get_object()
        payment_status = request.data.get('payment_status')
        delivery_status = request.data.get('delivery_status')

        if payment_status:
            order.payment_status = payment_status

        if delivery_status:
            order.delivery_status = delivery_status

        order.save()

        serializer = self.get_serializer(order)
        return Response(serializer.data)

# Review Views
class ReviewViewSet(viewsets.ModelViewSet):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        return Review.objects.all()

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

# Offer Views
class OfferViewSet(viewsets.ModelViewSet):
    queryset = Offer.objects.filter(is_active=True)
    serializer_class = OfferSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsManager()]  # Allow both managers and admins
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def active(self, request):
        from django.utils import timezone
        now = timezone.now()

        offers = Offer.objects.filter(
            is_active=True,
            start_date__lte=now,
            end_date__gte=now
        )

        serializer = self.get_serializer(offers, many=True)
        return Response(serializer.data)

# User Behavior Tracking
class UserBehaviorView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        action = request.data.get('action')
        product_id = request.data.get('product_id')
        metadata = request.data.get('metadata', {})

        # Get additional tracking data
        session_id = request.data.get('session_id')
        page = metadata.get('page')

        # Get client info
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        referrer = request.META.get('HTTP_REFERER', '')

        # Create behavior object
        behavior_data = {
            'user': request.user,
            'action': action,
            'metadata': metadata,
            'session_id': session_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'referrer': referrer,
            'page': page
        }

        # Add product if provided
        if product_id:
            try:
                product = Product.objects.get(id=product_id)
                behavior_data['product'] = product
            except Product.DoesNotExist:
                # If product doesn't exist, we still track the behavior without the product
                pass

        behavior = UserBehavior.objects.create(**behavior_data)

        serializer = UserBehaviorSerializer(behavior)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def get(self, request):
        """Get user behavior data for the current user or a specific user (admin only)"""
        user_id = request.query_params.get('user')

        if user_id and request.user.is_staff:
            # Admin can view any user's behavior
            behaviors = UserBehavior.objects.filter(user__id=user_id).order_by('-action_time')
        else:
            # Regular users can only view their own behavior
            behaviors = UserBehavior.objects.filter(user=request.user).order_by('-action_time')

        page = self.paginate_queryset(behaviors, request)
        if page is not None:
            serializer = UserBehaviorSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = UserBehaviorSerializer(behaviors, many=True)
        return Response(serializer.data)

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

# Home Page Data
class HomePageView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        # Get featured products
        featured_products = Product.objects.filter(is_active=True)[:8]

        # Get categories
        categories = Category.objects.filter(parent_category=None)[:6]

        # Get active offers
        from django.utils import timezone
        now = timezone.now()
        offers = Offer.objects.filter(
            is_active=True,
            start_date__lte=now,
            end_date__gte=now
        )[:4]

        return Response({
            'featured_products': ProductSerializer(featured_products, many=True).data,
            'categories': CategorySerializer(categories, many=True).data,
            'offers': OfferSerializer(offers, many=True).data
        })

# Search View
class SearchView(APIView):
    """
    API endpoint for searching products using Typesense.

    This view provides powerful search capabilities including:
    - Full-text search across product fields
    - Filtering by category, price range, etc.
    - Sorting by relevance, price, etc.
    - Pagination of results
    """
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        """
        Handle GET requests for product search.

        Query parameters:
        - q: Search query string
        - category: Category ID to filter by
        - min_price: Minimum price filter
        - max_price: Maximum price filter
        - sort_by: Field to sort by (e.g., price:asc, price:desc)
        - page: Page number for pagination
        - per_page: Number of results per page

        Returns:
            Response with search results and metadata
        """
        import os
        from .typesense_utils import search_products

        # Extract search parameters
        query = request.query_params.get('q', '')
        category_id = request.query_params.get('category', None)
        min_price = request.query_params.get('min_price', None)
        max_price = request.query_params.get('max_price', None)
        sort_by = request.query_params.get('sort_by', None)
        page = int(request.query_params.get('page', 1))
        per_page = int(request.query_params.get('per_page', 10))

        # Check if Typesense is enabled
        typesense_enabled = os.environ.get('TYPESENSE_ENABLED', 'False').lower() == 'true'

        # If Typesense is not enabled, use basic search
        if not typesense_enabled:
            # Basic search using Django ORM
            products = Product.objects.filter(is_active=True)

            if query:
                products = products.filter(title__icontains=query) | products.filter(description__icontains=query)

            if category_id:
                products = products.filter(category_id=category_id)

            # Apply pagination
            start = (page - 1) * per_page
            end = start + per_page
            paginated_products = products[start:end]

            serializer = ProductSerializer(paginated_products, many=True)

            return Response({
                'results': serializer.data,
                'found': products.count(),
                'page': page,
                'per_page': per_page,
                'total_pages': (products.count() + per_page - 1) // per_page,
                'search_type': 'basic'
            })

        # Build filters for Typesense
        filters = {}
        if category_id:
            filters['category_id'] = category_id

        # Add price range filter if provided
        price_filter = []
        if min_price:
            price_filter.append(f"price:>={float(min_price)}")
        if max_price:
            price_filter.append(f"price:<={float(max_price)}")
        if price_filter:
            filters['price_filter'] = ' && '.join(price_filter)

        # Only show active products
        filters['is_active'] = True

        try:
            # Search using Typesense
            search_results = search_products(
                query=query,
                filters=filters,
                sort_by=sort_by,
                page=page,
                per_page=per_page
            )

            # Extract product IDs from search results
            product_ids = [hit['document']['id'] for hit in search_results['hits']]

            # Fetch full product objects from database
            products = Product.objects.filter(id__in=product_ids)

            # Maintain the order from search results
            product_dict = {str(p.id): p for p in products}
            ordered_products = [product_dict[pid] for pid in product_ids if pid in product_dict]

            # Serialize and return results
            serializer = ProductSerializer(ordered_products, many=True)

            return Response({
                'results': serializer.data,
                'found': search_results['found'],
                'page': page,
                'per_page': per_page,
                'total_pages': (search_results['found'] + per_page - 1) // per_page,
                'search_type': 'typesense'
            })

        except Exception as e:
            # Fallback to basic search if Typesense fails
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Typesense search failed, falling back to basic search: {str(e)}")

            # Basic search using Django ORM
            products = Product.objects.filter(is_active=True)

            if query:
                products = products.filter(title__icontains=query) | products.filter(description__icontains=query)

            if category_id:
                products = products.filter(category_id=category_id)

            # Apply pagination
            start = (page - 1) * per_page
            end = start + per_page
            paginated_products = products[start:end]

            serializer = ProductSerializer(paginated_products, many=True)

            return Response({
                'results': serializer.data,
                'found': products.count(),
                'page': page,
                'per_page': per_page,
                'total_pages': (products.count() + per_page - 1) // per_page,
                'fallback': True  # Indicate that we're using the fallback search
            })

class ShippingOptionsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # In a real app, these would come from a database
        shipping_options = [
            {
                'id': 'standard',
                'name': 'Standard Shipping',
                'description': 'Delivery in 5-7 business days',
                'price': 5.99,
                'estimated_days': 7
            },
            {
                'id': 'express',
                'name': 'Express Shipping',
                'description': 'Delivery in 2-3 business days',
                'price': 12.99,
                'estimated_days': 3
            },
            {
                'id': 'overnight',
                'name': 'Overnight Shipping',
                'description': 'Next day delivery for orders placed before 2pm',
                'price': 19.99,
                'estimated_days': 1
            }
        ]
        return Response(shipping_options)

class PaymentProcessingView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        payment_method_id = request.data.get('payment_method_id')
        order_id = request.data.get('order_id')

        if not order_id:
            return Response({
                'error': 'Order ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get the order
            order = Order.objects.get(id=order_id, user=request.user)

            # If payment is already processed, return success
            if order.payment_status == 'paid':
                return Response({
                    'success': True,
                    'message': 'Payment already processed'
                })

            # If there's an existing payment intent, use it
            if order.stripe_payment_intent_id:
                payment_intent = retrieve_payment_intent(order.stripe_payment_intent_id)

                # If payment intent is already succeeded, update order
                if payment_intent.status == 'succeeded':
                    order.payment_status = 'paid'
                    order.save()

                    # Create or update StripePayment record
                    payment, created = StripePayment.objects.update_or_create(
                        order=order,
                        payment_intent_id=payment_intent.id,
                        defaults={
                            'amount': order.total_price,
                            'status': 'succeeded',
                            'payment_method_id': payment_intent.payment_method,
                            'metadata': payment_intent.metadata
                        }
                    )

                    return Response({
                        'success': True,
                        'payment_intent': payment_intent.id,
                        'message': 'Payment processed successfully'
                    })

                # If payment intent needs confirmation with a payment method
                if payment_method_id and payment_intent.status in ['requires_payment_method', 'requires_confirmation']:
                    try:
                        payment_intent = confirm_payment_intent(payment_intent.id, payment_method_id)
                        order.stripe_payment_method_id = payment_method_id
                        order.save(update_fields=['stripe_payment_method_id'])

                        return Response({
                            'success': True,
                            'requires_action': payment_intent.status == 'requires_action',
                            'payment_intent_client_secret': payment_intent.client_secret,
                            'message': 'Payment intent confirmed'
                        })
                    except Exception as e:
                        return Response({
                            'error': str(e)
                        }, status=status.HTTP_400_BAD_REQUEST)

            # Create a new payment intent
            try:
                payment_intent_data = create_payment_intent(
                    order,
                    customer_email=request.user.email
                )

                # Create StripePayment record
                payment = StripePayment.objects.create(
                    order=order,
                    payment_intent_id=payment_intent_data['payment_intent_id'],
                    amount=order.total_price,
                    status='pending'
                )

                return Response({
                    'success': True,
                    'payment_intent_client_secret': payment_intent_data['client_secret'],
                    'payment_intent_id': payment_intent_data['payment_intent_id'],
                    'message': 'Payment intent created'
                })
            except Exception as e:
                return Response({
                    'error': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)

        except Order.DoesNotExist:
            return Response({
                'error': 'Order not found'
            }, status=status.HTTP_404_NOT_FOUND)

class StripeWebhookView(APIView):
    """
    Handle Stripe webhook events.

    This view receives webhook events from Stripe and processes them accordingly.
    It handles payment intents, refunds, and other payment-related events.
    """
    permission_classes = [permissions.AllowAny]  # Stripe needs to access this endpoint without authentication

    def post(self, request):
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

        if not sig_header:
            return Response({'error': 'Stripe signature header is missing'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            event = construct_event(payload, sig_header)
            if not event:
                return Response({'error': 'Invalid Stripe webhook signature'}, status=status.HTTP_400_BAD_REQUEST)

            # Handle the event based on its type
            if event.type == 'payment_intent.succeeded':
                self.handle_payment_intent_succeeded(event.data.object)
            elif event.type == 'payment_intent.payment_failed':
                self.handle_payment_intent_failed(event.data.object)
            elif event.type == 'charge.refunded':
                self.handle_charge_refunded(event.data.object)

            # Return a success response to Stripe
            return Response({'status': 'success'})

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def handle_payment_intent_succeeded(self, payment_intent):
        """Handle successful payment intent"""
        try:
            # Find the order associated with this payment intent
            order = Order.objects.get(stripe_payment_intent_id=payment_intent.id)

            # Update order status
            order.payment_status = 'paid'
            order.save()

            # Update or create StripePayment record
            payment, created = StripePayment.objects.update_or_create(
                order=order,
                payment_intent_id=payment_intent.id,
                defaults={
                    'amount': order.total_price,
                    'status': 'succeeded',
                    'payment_method_id': payment_intent.payment_method,
                    'metadata': payment_intent.metadata
                }
            )
        except Order.DoesNotExist:
            # Log error - payment intent doesn't match any order
            print(f"Error: Payment intent {payment_intent.id} doesn't match any order")

    def handle_payment_intent_failed(self, payment_intent):
        """Handle failed payment intent"""
        try:
            # Find the order associated with this payment intent
            order = Order.objects.get(stripe_payment_intent_id=payment_intent.id)

            # Update order status
            order.payment_status = 'failed'
            order.save()

            # Update or create StripePayment record
            payment, created = StripePayment.objects.update_or_create(
                order=order,
                payment_intent_id=payment_intent.id,
                defaults={
                    'amount': order.total_price,
                    'status': 'failed',
                    'payment_method_id': payment_intent.payment_method,
                    'metadata': payment_intent.metadata
                }
            )
        except Order.DoesNotExist:
            # Log error - payment intent doesn't match any order
            print(f"Error: Payment intent {payment_intent.id} doesn't match any order")

    def handle_charge_refunded(self, charge):
        """Handle refunded charge"""
        try:
            # Find the payment intent associated with this charge
            payment_intent_id = charge.payment_intent

            # Find the order associated with this payment intent
            order = Order.objects.get(stripe_payment_intent_id=payment_intent_id)

            # Update order status
            order.payment_status = 'refunded'
            order.save()

            # Update or create StripePayment record
            payment, created = StripePayment.objects.update_or_create(
                order=order,
                payment_intent_id=payment_intent_id,
                defaults={
                    'amount': order.total_price,
                    'status': 'refunded',
                    'metadata': {'charge_id': charge.id}
                }
            )
        except Order.DoesNotExist:
            # Log error - payment intent doesn't match any order
            print(f"Error: Payment intent {payment_intent_id} doesn't match any order")


class PromoCodeView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        code = request.data.get('code')
        cart_id = request.data.get('cart_id')

        if not code:
            return Response({
                'error': 'Promo code is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # In a real app, you would check the promo code against a database
        # For this example, we'll just hardcode some valid promo codes
        valid_promo_codes = {
            'WELCOME10': {'discount_type': 'percentage', 'value': 10},
            'SUMMER20': {'discount_type': 'percentage', 'value': 20},
            'FREESHIP': {'discount_type': 'shipping', 'value': 100},
            'FLAT25': {'discount_type': 'fixed', 'value': 25}
        }

        if code.upper() in valid_promo_codes:
            promo = valid_promo_codes[code.upper()]

            # If a cart ID was provided, apply the discount to the cart
            if cart_id:
                try:
                    cart = Cart.objects.get(id=cart_id, created_by=request.user)
                    # In a real app, you would store the applied promo code on the cart
                    # and calculate the discounted total

                    return Response({
                        'success': True,
                        'discount_type': promo['discount_type'],
                        'discount_value': promo['value'],
                        'message': f"Promo code '{code}' applied successfully"
                    })
                except Cart.DoesNotExist:
                    return Response({
                        'error': 'Cart not found'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                return Response({
                    'success': True,
                    'discount_type': promo['discount_type'],
                    'discount_value': promo['value'],
                    'message': f"Promo code '{code}' is valid"
                })
        else:
            return Response({
                'error': 'Invalid promo code'
            }, status=status.HTTP_400_BAD_REQUEST)


# Inventory Management Views
class InventoryManagementViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing product inventory.

    This ViewSet provides CRUD operations for inventory management:
    - List all inventory changes with filtering and search
    - Create new inventory changes (add/subtract stock)
    - Retrieve details of specific inventory changes
    - Generate inventory reports and statistics

    Only managers and admins can access these endpoints.
    """
    queryset = InventoryChange.objects.all().order_by('-timestamp')
    serializer_class = InventoryChangeSerializer
    permission_classes = [IsManager]
    filter_backends = [filters.SearchFilter]
    search_fields = ['product__title', 'notes', 'change_type']

    def get_queryset(self):
        """
        Filter inventory changes based on query parameters.

        Supported filters:
        - product_id: Filter by specific product
        - change_type: Filter by type of inventory change (add, subtract, etc.)
        - start_date: Filter by changes after this date
        - end_date: Filter by changes before this date

        Returns:
            QuerySet of filtered InventoryChange objects
        """
        queryset = super().get_queryset()

        # Filter by product if product_id is provided
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product__id=product_id)

        # Filter by change type if provided
        change_type = self.request.query_params.get('change_type')
        if change_type:
            queryset = queryset.filter(change_type=change_type)

        # Filter by date range if provided
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)
        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)

        return queryset

    def perform_create(self, serializer):
        """
        Override create to handle inventory changes properly.

        This method processes inventory change requests by:
        1. Extracting data from the serializer
        2. Finding the product and related order (if any)
        3. Calling the appropriate inventory management method on the product
        4. Handling any validation errors that occur

        The method uses different product methods based on the change_type:
        - 'add': Increases inventory
        - 'subtract': Decreases inventory
        - 'order': Decreases inventory due to an order
        - 'return': Increases inventory due to a return
        - 'adjustment': Manual adjustment with custom reason

        Args:
            serializer: The validated serializer instance

        Returns:
            Response with success message or error details
        """
        # Extract data from serializer
        product_id = serializer.validated_data.get('product_id')
        quantity_change = serializer.validated_data.get('quantity_change')
        change_type = serializer.validated_data.get('change_type')
        notes = serializer.validated_data.get('notes')
        reference_order_id = serializer.validated_data.get('reference_order_id')

        # Get the product and order objects
        product = get_object_or_404(Product, id=product_id)
        reference_order = None

        if reference_order_id:
            reference_order = get_object_or_404(Order, id=reference_order_id)

        # Use the product's inventory management methods based on change type
        try:
            if change_type == 'add':
                product.add_inventory(quantity_change, self.request.user, notes)
            elif change_type == 'subtract':
                product.subtract_inventory(quantity_change, self.request.user, notes)
            elif change_type == 'order' and reference_order:
                product.order_inventory(quantity_change, self.request.user, reference_order, notes)
            elif change_type == 'return' and reference_order:
                product.return_inventory(quantity_change, self.request.user, reference_order, notes)
            elif change_type == 'adjustment':
                product.update_inventory(quantity_change, 'adjustment', self.request.user, notes=notes)
            else:
                return Response(
                    {'error': 'Invalid change type or missing required reference'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response({'success': True}, status=status.HTTP_201_CREATED)
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """
        Get inventory summary statistics.

        This endpoint provides a dashboard overview of inventory status:
        - Count of products with low stock (less than 10 items)
        - Total value of all inventory in stock
        - Recent inventory changes for activity monitoring

        Returns:
            Response with summary statistics in JSON format
        """
        # Get products with low stock (less than 10 items)
        low_stock = Product.objects.filter(stock_quantity__lt=10).count()

        # Get total inventory value by multiplying each product's price by its quantity
        total_value = Product.objects.annotate(
            value=F('stock_quantity') * F('price')
        ).aggregate(total=Sum('value'))

        # Get recent inventory changes for activity monitoring
        recent_changes = InventoryChange.objects.all().order_by('-timestamp')[:5]

        return Response({
            'low_stock_count': low_stock,
            'total_inventory_value': total_value['total'] or 0,
            'recent_changes': InventoryChangeSerializer(recent_changes, many=True).data
        })
