"""
Stripe utility functions for the eCommerce platform.

This module contains helper functions for interacting with the Stripe API,
including creating payment intents, handling webhooks, and processing payments.
"""
import stripe
from django.conf import settings
from decimal import Decimal

# Configure Stripe API key
stripe.api_key = settings.STRIPE_SECRET_KEY
stripe.api_version = settings.STRIPE_API_VERSION

def create_payment_intent(order, customer_email=None):
    """
    Create a Stripe PaymentIntent for an order.
    
    Args:
        order: Order model instance
        customer_email: Optional customer email for receipt
        
    Returns:
        dict: Payment intent details including client_secret
    """
    # Convert decimal to integer cents for Stripe
    amount_cents = int(order.total_price * 100)
    
    # Create metadata for the payment intent
    metadata = {
        'order_id': str(order.id),
        'user_id': str(order.user.id),
    }
    
    # Create the payment intent
    intent = stripe.PaymentIntent.create(
        amount=amount_cents,
        currency='usd',
        metadata=metadata,
        receipt_email=customer_email,
        payment_method_types=['card'],
    )
    
    # Update the order with the payment intent ID
    order.stripe_payment_intent_id = intent.id
    order.save(update_fields=['stripe_payment_intent_id'])
    
    return {
        'client_secret': intent.client_secret,
        'payment_intent_id': intent.id,
    }

def retrieve_payment_intent(payment_intent_id):
    """
    Retrieve a payment intent from Stripe.
    
    Args:
        payment_intent_id: Stripe payment intent ID
        
    Returns:
        Stripe PaymentIntent object
    """
    return stripe.PaymentIntent.retrieve(payment_intent_id)

def confirm_payment_intent(payment_intent_id, payment_method_id=None):
    """
    Confirm a payment intent.
    
    Args:
        payment_intent_id: Stripe payment intent ID
        payment_method_id: Optional payment method ID
        
    Returns:
        Stripe PaymentIntent object
    """
    if payment_method_id:
        return stripe.PaymentIntent.confirm(
            payment_intent_id,
            payment_method=payment_method_id
        )
    return stripe.PaymentIntent.confirm(payment_intent_id)

def cancel_payment_intent(payment_intent_id):
    """
    Cancel a payment intent.
    
    Args:
        payment_intent_id: Stripe payment intent ID
        
    Returns:
        Stripe PaymentIntent object
    """
    return stripe.PaymentIntent.cancel(payment_intent_id)

def construct_event(payload, sig_header):
    """
    Construct a Stripe event from webhook payload.
    
    Args:
        payload: Request body from Stripe webhook
        sig_header: Stripe signature header
        
    Returns:
        Stripe Event object
    """
    try:
        return stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError:
        # Invalid payload
        return None
    except stripe.error.SignatureVerificationError:
        # Invalid signature
        return None
