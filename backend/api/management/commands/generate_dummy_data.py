import random
import uuid
from datetime import timed<PERSON>ta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model
from api.models import (
    Role, UserRole, Category, Product, Cart, CartItem,
    Order, OrderLine, Review, Offer
)

User = get_user_model()

class Command(BaseCommand):
    help = 'Generates dummy data for the eCommerce platform'

    def handle(self, *args, **kwargs):
        self.stdout.write('Generating dummy data...')
        
        # Create roles
        self.create_roles()
        
        # Create users
        self.create_users()
        
        # Create categories
        self.create_categories()
        
        # Create products
        self.create_products()
        
        # Create carts and orders
        self.create_carts_and_orders()
        
        # Create reviews
        self.create_reviews()
        
        # Create offers
        self.create_offers()
        
        self.stdout.write(self.style.SUCCESS('Successfully generated dummy data!'))
    
    def create_roles(self):
        roles = [
            {'name': 'Admin', 'description': 'Administrator with full access'},
            {'name': 'Customer', 'description': 'Regular customer'},
            {'name': 'Moderator', 'description': 'Content moderator'},
            {'name': 'Vendor', 'description': 'Product vendor'}
        ]
        
        for role_data in roles:
            Role.objects.get_or_create(name=role_data['name'], defaults=role_data)
        
        self.stdout.write('Created roles')
    
    def create_users(self):
        # Create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
        
        # Create regular users
        for i in range(1, 11):
            user, created = User.objects.get_or_create(
                username=f'user{i}',
                defaults={
                    'email': f'user{i}@example.com',
                    'first_name': f'User {i}',
                    'last_name': f'Lastname {i}'
                }
            )
            
            if created:
                user.set_password('password123')
                user.save()
        
        # Assign roles
        admin_role = Role.objects.get(name='Admin')
        customer_role = Role.objects.get(name='Customer')
        moderator_role = Role.objects.get(name='Moderator')
        
        # Assign admin role
        UserRole.objects.get_or_create(user=admin_user, role=admin_role)
        
        # Assign customer role to all users
        for user in User.objects.all():
            UserRole.objects.get_or_create(user=user, role=customer_role)
        
        # Assign moderator role to a couple of users
        for user in User.objects.filter(username__in=['user1', 'user2']):
            UserRole.objects.get_or_create(user=user, role=moderator_role)
        
        self.stdout.write('Created users and assigned roles')
    
    def create_categories(self):
        categories = [
            {'name': 'Electronics', 'description': 'Electronic devices and accessories'},
            {'name': 'Clothing', 'description': 'Apparel and fashion items'},
            {'name': 'Home & Kitchen', 'description': 'Home appliances and kitchen essentials'},
            {'name': 'Books', 'description': 'Books, e-books, and audiobooks'},
            {'name': 'Sports & Outdoors', 'description': 'Sports equipment and outdoor gear'}
        ]
        
        for category_data in categories:
            Category.objects.get_or_create(name=category_data['name'], defaults=category_data)
        
        # Create subcategories
        subcategories = [
            {'name': 'Smartphones', 'parent_category': 'Electronics', 'description': 'Mobile phones and accessories'},
            {'name': 'Laptops', 'parent_category': 'Electronics', 'description': 'Laptops and accessories'},
            {'name': 'Men\'s Clothing', 'parent_category': 'Clothing', 'description': 'Clothing for men'},
            {'name': 'Women\'s Clothing', 'parent_category': 'Clothing', 'description': 'Clothing for women'},
            {'name': 'Kitchen Appliances', 'parent_category': 'Home & Kitchen', 'description': 'Appliances for the kitchen'},
            {'name': 'Fiction', 'parent_category': 'Books', 'description': 'Fiction books'},
            {'name': 'Non-Fiction', 'parent_category': 'Books', 'description': 'Non-fiction books'},
            {'name': 'Fitness Equipment', 'parent_category': 'Sports & Outdoors', 'description': 'Equipment for fitness and exercise'}
        ]
        
        for subcategory_data in subcategories:
            parent_name = subcategory_data.pop('parent_category')
            parent = Category.objects.get(name=parent_name)
            
            Category.objects.get_or_create(
                name=subcategory_data['name'],
                defaults={**subcategory_data, 'parent_category': parent}
            )
        
        self.stdout.write('Created categories and subcategories')
    
    def create_products(self):
        # Get all subcategories
        subcategories = Category.objects.filter(parent_category__isnull=False)
        
        # Product data by subcategory
        products_by_subcategory = {
            'Smartphones': [
                {'title': 'Premium Smartphone X', 'price': 999.99, 'stock_quantity': 50},
                {'title': 'Budget Smartphone Y', 'price': 299.99, 'stock_quantity': 100},
                {'title': 'Smartphone Case', 'price': 19.99, 'stock_quantity': 200},
                {'title': 'Screen Protector', 'price': 9.99, 'stock_quantity': 300}
            ],
            'Laptops': [
                {'title': 'Gaming Laptop Pro', 'price': 1499.99, 'stock_quantity': 30},
                {'title': 'Ultrabook Slim', 'price': 899.99, 'stock_quantity': 40},
                {'title': 'Laptop Backpack', 'price': 49.99, 'stock_quantity': 100},
                {'title': 'Laptop Cooling Pad', 'price': 29.99, 'stock_quantity': 80}
            ],
            'Men\'s Clothing': [
                {'title': 'Men\'s T-Shirt', 'price': 24.99, 'stock_quantity': 150},
                {'title': 'Men\'s Jeans', 'price': 49.99, 'stock_quantity': 100},
                {'title': 'Men\'s Hoodie', 'price': 39.99, 'stock_quantity': 80},
                {'title': 'Men\'s Sneakers', 'price': 79.99, 'stock_quantity': 60}
            ],
            'Women\'s Clothing': [
                {'title': 'Women\'s Blouse', 'price': 29.99, 'stock_quantity': 120},
                {'title': 'Women\'s Dress', 'price': 59.99, 'stock_quantity': 90},
                {'title': 'Women\'s Jeans', 'price': 54.99, 'stock_quantity': 100},
                {'title': 'Women\'s Boots', 'price': 89.99, 'stock_quantity': 50}
            ],
            'Kitchen Appliances': [
                {'title': 'Coffee Maker', 'price': 79.99, 'stock_quantity': 40},
                {'title': 'Blender', 'price': 49.99, 'stock_quantity': 60},
                {'title': 'Toaster', 'price': 34.99, 'stock_quantity': 70},
                {'title': 'Electric Kettle', 'price': 29.99, 'stock_quantity': 80}
            ],
            'Fiction': [
                {'title': 'Mystery Novel', 'price': 14.99, 'stock_quantity': 100},
                {'title': 'Science Fiction Book', 'price': 16.99, 'stock_quantity': 90},
                {'title': 'Fantasy Series', 'price': 39.99, 'stock_quantity': 50},
                {'title': 'Romance Novel', 'price': 12.99, 'stock_quantity': 120}
            ],
            'Non-Fiction': [
                {'title': 'Self-Help Book', 'price': 19.99, 'stock_quantity': 80},
                {'title': 'Cookbook', 'price': 24.99, 'stock_quantity': 70},
                {'title': 'Biography', 'price': 22.99, 'stock_quantity': 60},
                {'title': 'History Book', 'price': 29.99, 'stock_quantity': 50}
            ],
            'Fitness Equipment': [
                {'title': 'Yoga Mat', 'price': 29.99, 'stock_quantity': 100},
                {'title': 'Dumbbells Set', 'price': 79.99, 'stock_quantity': 40},
                {'title': 'Resistance Bands', 'price': 19.99, 'stock_quantity': 120},
                {'title': 'Jump Rope', 'price': 14.99, 'stock_quantity': 150}
            ]
        }
        
        # Create products for each subcategory
        for subcategory in subcategories:
            if subcategory.name in products_by_subcategory:
                for product_data in products_by_subcategory[subcategory.name]:
                    summary = f"A great {product_data['title']} for all your needs"
                    description = f"This {product_data['title']} is a high-quality product that will meet all your expectations. It comes with a warranty and excellent customer support."
                    
                    Product.objects.get_or_create(
                        title=product_data['title'],
                        defaults={
                            **product_data,
                            'category': subcategory,
                            'summary': summary,
                            'description': description,
                            'tags': [subcategory.name.lower(), 'featured'] if random.random() > 0.7 else [subcategory.name.lower()],
                            'is_active': True
                        }
                    )
        
        self.stdout.write('Created products')
    
    def create_carts_and_orders(self):
        users = User.objects.filter(is_staff=False)
        products = Product.objects.filter(is_active=True)
        
        for user in users:
            # Create active cart
            cart, _ = Cart.objects.get_or_create(
                created_by=user,
                status='active',
                defaults={'created_at': timezone.now()}
            )
            
            # Add random items to cart
            for _ in range(random.randint(1, 5)):
                product = random.choice(products)
                quantity = random.randint(1, 3)
                
                CartItem.objects.get_or_create(
                    cart=cart,
                    product=product,
                    defaults={
                        'price': product.price,
                        'quantity': quantity
                    }
                )
            
            # Create completed orders
            for _ in range(random.randint(0, 3)):
                # Create order
                order_products = random.sample(list(products), random.randint(1, 5))
                total_price = sum(product.price * random.randint(1, 3) for product in order_products)
                
                order = Order.objects.create(
                    user=user,
                    total_price=total_price,
                    payment_status=random.choice(['pending', 'paid']),
                    delivery_status=random.choice(['processing', 'shipped', 'delivered']),
                    created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                )
                
                # Create order lines
                for product in order_products:
                    quantity = random.randint(1, 3)
                    OrderLine.objects.create(
                        order=order,
                        product=product,
                        price=product.price,
                        quantity=quantity
                    )
        
        self.stdout.write('Created carts and orders')
    
    def create_reviews(self):
        users = User.objects.filter(is_staff=False)
        products = Product.objects.filter(is_active=True)
        
        for product in products:
            # Add 0-5 reviews per product
            for _ in range(random.randint(0, 5)):
                user = random.choice(users)
                rating = random.uniform(3.0, 5.0)  # Mostly positive reviews
                
                # Skip if review already exists
                if Review.objects.filter(user=user, product=product).exists():
                    continue
                
                Review.objects.create(
                    user=user,
                    product=product,
                    rating=rating,
                    comment=f"This is a {'great' if rating > 4 else 'good'} product. {'Highly recommended!' if rating > 4.5 else 'Recommended.'}"
                )
        
        self.stdout.write('Created reviews')
    
    def create_offers(self):
        products = Product.objects.filter(is_active=True)
        
        # Create offers for random products
        for _ in range(10):
            product = random.choice(products)
            offer_type = random.choice(['percentage', 'flat'])
            discount_value = random.randint(5, 30) if offer_type == 'percentage' else random.randint(5, 50)
            
            start_date = timezone.now() - timedelta(days=random.randint(0, 10))
            end_date = start_date + timedelta(days=random.randint(5, 30))
            
            Offer.objects.create(
                product=product,
                name=f"{discount_value}% Off" if offer_type == 'percentage' else f"${discount_value} Off",
                offer_type=offer_type,
                discount_value=discount_value,
                start_date=start_date,
                end_date=end_date,
                is_festival=random.random() > 0.8,
                is_active=True
            )
        
        self.stdout.write('Created offers')
