"""
Management command to reindex all products in Typesense.
"""
import time
import logging
from django.core.management.base import BaseCommand
from django.conf import settings
from api.models import Product
from api.typesense_utils import get_typesense_client, create_product_schema, index_product

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Reindex all products in Typesense'

    def add_arguments(self, parser):
        parser.add_argument(
            '--recreate',
            action='store_true',
            help='Recreate the collection before indexing',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of products to index in each batch',
        )

    def handle(self, *args, **options):
        recreate = options['recreate']
        batch_size = options['batch_size']
        
        client = get_typesense_client()
        collection_name = settings.TYPESENSE['collection_name']
        
        # Create schema if it doesn't exist
        self.stdout.write(self.style.NOTICE('Ensuring Typesense schema exists...'))
        create_product_schema()
        
        # Recreate collection if requested
        if recreate:
            self.stdout.write(self.style.WARNING(f'Recreating collection {collection_name}...'))
            try:
                client.collections[collection_name].delete()
                self.stdout.write(self.style.SUCCESS(f'Collection {collection_name} deleted.'))
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Error deleting collection: {str(e)}'))
            
            # Create the schema again
            create_product_schema()
            self.stdout.write(self.style.SUCCESS(f'Collection {collection_name} recreated.'))
        
        # Get all products
        products = Product.objects.all()
        total_products = products.count()
        self.stdout.write(self.style.NOTICE(f'Found {total_products} products to index.'))
        
        # Index products in batches
        indexed_count = 0
        error_count = 0
        start_time = time.time()
        
        for i in range(0, total_products, batch_size):
            batch = products[i:i+batch_size]
            batch_start_time = time.time()
            
            for product in batch:
                try:
                    index_product(product)
                    indexed_count += 1
                except Exception as e:
                    error_count += 1
                    logger.error(f"Error indexing product {product.id}: {str(e)}")
            
            batch_end_time = time.time()
            batch_duration = batch_end_time - batch_start_time
            
            self.stdout.write(self.style.SUCCESS(
                f'Indexed batch {i//batch_size + 1}/{(total_products + batch_size - 1)//batch_size} '
                f'({indexed_count}/{total_products}, {error_count} errors) '
                f'in {batch_duration:.2f}s'
            ))
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        self.stdout.write(self.style.SUCCESS(
            f'Indexing complete. Indexed {indexed_count} products with {error_count} errors '
            f'in {total_duration:.2f}s'
        ))
