import os
import random
from datetime import timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from django.conf import settings

from api.models import Product, ProductImage

# Sample image URLs for testing
# In a real scenario, you would download these or use local files
SAMPLE_IMAGE_URLS = [
    'https://via.placeholder.com/800x600/FF5733/FFFFFF?text=Product+Image+1',
    'https://via.placeholder.com/800x600/33FF57/FFFFFF?text=Product+Image+2',
    'https://via.placeholder.com/800x600/5733FF/FFFFFF?text=Product+Image+3',
    'https://via.placeholder.com/800x600/33FFFF/FFFFFF?text=Product+Image+4',
    'https://via.placeholder.com/800x600/FF33FF/FFFFFF?text=Product+Image+5',
]

class Command(BaseCommand):
    help = 'Generates product images for existing products'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing product images before generating new ones',
        )

    def handle(self, *args, **options):
        self.stdout.write('Generating product images...')
        
        if options['clear']:
            self.stdout.write('Clearing existing product images...')
            ProductImage.objects.all().delete()
            # Also clear main product images
            for product in Product.objects.all():
                product.picture = None
                product.save()
        
        # Create media directory if it doesn't exist
        media_root = settings.MEDIA_ROOT
        products_dir = os.path.join(media_root, 'products')
        os.makedirs(products_dir, exist_ok=True)
        
        # Get all products
        products = Product.objects.all()
        
        if not products:
            self.stdout.write(self.style.WARNING('No products found. Please run generate_dummy_data command first.'))
            return
        
        self.stdout.write(f'Found {products.count()} products. Adding images...')
        
        for product in products:
            # Add main product image
            self.add_main_image(product)
            
            # Add additional images (1-4 additional images)
            num_additional_images = random.randint(1, 4)
            self.stdout.write(f'Adding {num_additional_images} additional images to {product.title}')
            
            for i in range(num_additional_images):
                self.add_additional_image(product, i)
        
        self.stdout.write(self.style.SUCCESS('Successfully generated product images!'))
    
    def add_main_image(self, product):
        """Add a main image to a product"""
        # In a real scenario, you would download the image or use a local file
        # For this example, we'll create a dummy file
        image_name = f"{product.slug}_main.jpg"
        
        # Create a dummy image file (in a real scenario, you would use actual image data)
        dummy_image = SimpleUploadedFile(
            name=image_name,
            content=b'dummy image content',
            content_type='image/jpeg'
        )
        
        # Save the image to the product
        product.picture.save(image_name, dummy_image, save=True)
        self.stdout.write(f'Added main image to {product.title}')
    
    def add_additional_image(self, product, order):
        """Add an additional image to a product"""
        # In a real scenario, you would download the image or use a local file
        # For this example, we'll create a dummy file
        image_name = f"{product.slug}_additional_{order + 1}.jpg"
        
        # Create a dummy image file
        dummy_image = SimpleUploadedFile(
            name=image_name,
            content=b'dummy image content',
            content_type='image/jpeg'
        )
        
        # Create the ProductImage object
        product_image = ProductImage(
            product=product,
            order=order,
            alt_text=f"Additional image {order + 1} for {product.title}"
        )
        
        # Save the image
        product_image.image.save(image_name, dummy_image, save=True)
