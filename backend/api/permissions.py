"""
Custom permission classes for the eCommerce API.
These classes define access control for different user roles.
"""
from rest_framework import permissions

class IsManager(permissions.BasePermission):
    """
    Custom permission to only allow managers to access the view.

    This permission checks if the user has the 'manager' role or is a staff member.
    Both managers and admin users (is_staff=True) are granted access.
    """
    def has_permission(self, request, view):
        # Check if user is authenticated
        if not request.user or not request.user.is_authenticated:
            return False

        # Check if user has manager role or is staff
        return request.user.user_roles.filter(role__name='manager').exists() or request.user.is_staff

class IsManagerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow managers to edit but anyone to view.

    - GET, HEAD, OPTIONS requests are allowed for any user
    - POST, PUT, PATCH, DELETE requests require manager role or staff status
    """
    def has_permission(self, request, view):
        # Allow GET, HEAD, OPTIONS requests
        if request.method in permissions.SAFE_METHODS:
            return True

        # Check if user is authenticated and has manager role
        if not request.user or not request.user.is_authenticated:
            return False

        return request.user.user_roles.filter(role__name='manager').exists() or request.user.is_staff
