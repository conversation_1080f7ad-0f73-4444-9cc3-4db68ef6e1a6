"""
Serializers for the eCommerce API.

This module contains serializer classes that convert model instances
to JSON representations and validate incoming data for API operations.
"""
from rest_framework import serializers
from .models import (
    User, Role, UserRole, Credential, SocialProfile,
    Category, Product, ProductImage, ProductModerationLog,
    Cart, CartItem, Order, OrderLine,
    Review, PersonalizedRecommendation, Offer, UserBehavior,
    InventoryChange, StripePayment
)

class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model.

    Provides a complete representation of user data for API responses,
    with sensitive fields marked as read-only.
    """
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'phone',
                  'avatar', 'locale', 'bio', 'company', 'email_validated',
                  'phone_validated', 'date_joined', 'last_login']
        read_only_fields = ['id', 'date_joined', 'last_login']

class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['id', 'name', 'description']

class UserRoleSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)

    class Meta:
        model = UserRole
        fields = ['id', 'user', 'role', 'assigned_at']
        read_only_fields = ['id', 'assigned_at']

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'parent_category', 'slug', 'name', 'description', 'tags', 'created_at', 'updated_at']
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at']

class ProductImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id', 'image', 'order', 'alt_text', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for the Product model.

    Provides a complete representation of product data including:
    - Nested category data
    - All product images (main + additional)
    - Inventory and pricing information

    Uses a write-only category_id field for creating/updating products
    while providing the full category object in responses.
    """
    category = CategorySerializer(read_only=True)
    category_id = serializers.UUIDField(write_only=True)
    additional_images = ProductImageSerializer(many=True, read_only=True)
    images = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = ['id', 'category', 'category_id', 'title', 'slug', 'summary', 'description',
                  'picture', 'price', 'discount_type', 'discount_value', 'tags',
                  'stock_quantity', 'is_active', 'created_at', 'updated_at',
                  'additional_images', 'images']
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at', 'images']

    def get_images(self, obj):
        """Return a list of all image URLs for the product"""
        return obj.all_images

class ProductModerationLogSerializer(serializers.ModelSerializer):
    moderator = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)

    class Meta:
        model = ProductModerationLog
        fields = ['id', 'product', 'moderator', 'action', 'comments', 'timestamp']
        read_only_fields = ['id', 'timestamp']

class CartItemSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = CartItem
        fields = ['id', 'cart', 'product', 'product_id', 'price', 'quantity', 'created_at']
        read_only_fields = ['id', 'created_at']

class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)

    class Meta:
        model = Cart
        fields = ['id', 'created_by', 'status', 'items', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class OrderLineSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)

    class Meta:
        model = OrderLine
        fields = ['id', 'order', 'product', 'price', 'quantity']
        read_only_fields = ['id']

class OrderSerializer(serializers.ModelSerializer):
    order_lines = OrderLineSerializer(many=True, read_only=True)
    stripe_payments = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Order
        fields = ['id', 'user', 'total_price', 'payment_status', 'delivery_status',
                 'stripe_payment_intent_id', 'stripe_payment_method_id',
                 'order_lines', 'stripe_payments', 'created_at']
        read_only_fields = ['id', 'created_at', 'stripe_payments']

    def get_stripe_payments(self, obj):
        from .serializers import StripePaymentSerializer
        payments = obj.stripe_payments.all()
        return StripePaymentSerializer(payments, many=True).data

class ReviewSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = Review
        fields = ['id', 'user', 'product', 'product_id', 'rating', 'comment', 'created_at']
        read_only_fields = ['id', 'created_at']

class OfferSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = Offer
        fields = ['id', 'product', 'product_id', 'name', 'offer_type', 'discount_value',
                  'start_date', 'end_date', 'is_festival', 'is_active']
        read_only_fields = ['id']

class UserBehaviorSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)

    class Meta:
        model = UserBehavior
        fields = ['id', 'user', 'action', 'product', 'metadata', 'action_time']
        read_only_fields = ['id', 'action_time']

class PersonalizedRecommendationSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)

    class Meta:
        model = PersonalizedRecommendation
        fields = ['id', 'user', 'product', 'recommendation_score', 'created_at']
        read_only_fields = ['id', 'created_at']

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    password_confirm = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})

    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'password_confirm', 'first_name', 'last_name']

    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError({"password_confirm": "Passwords do not match."})
        return data

    def create(self, validated_data):
        validated_data.pop('password_confirm')
        user = User.objects.create_user(**validated_data)
        return user


class StripePaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for the StripePayment model.

    Provides a complete representation of Stripe payment data including:
    - Nested order data
    - Payment intent and method IDs
    - Payment status and timestamps
    """
    order = OrderSerializer(read_only=True)
    order_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = StripePayment
        fields = ['id', 'order', 'order_id', 'payment_intent_id', 'payment_method_id',
                  'amount', 'currency', 'status', 'created_at', 'updated_at', 'metadata']
        read_only_fields = ['id', 'created_at', 'updated_at']


class InventoryChangeSerializer(serializers.ModelSerializer):
    """
    Serializer for the InventoryChange model.

    Provides a complete representation of inventory changes including:
    - Nested product data
    - User who made the change
    - Reference to related order (if applicable)
    - Detailed change information (quantities, timestamps, etc.)

    Uses write-only ID fields for creating records while providing
    full nested objects in responses.
    """
    product = ProductSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)
    changed_by = UserSerializer(read_only=True)
    reference_order = OrderSerializer(read_only=True)
    reference_order_id = serializers.UUIDField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = InventoryChange
        fields = ['id', 'product', 'product_id', 'quantity_change', 'change_type',
                  'previous_quantity', 'new_quantity', 'changed_by', 'reference_order',
                  'reference_order_id', 'notes', 'timestamp']
        read_only_fields = ['id', 'previous_quantity', 'new_quantity', 'timestamp']
