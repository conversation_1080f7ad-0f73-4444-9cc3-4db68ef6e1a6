"""
URL configuration for the eCommerce API.

This module defines all API endpoints and their corresponding views.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenRefreshView

from .views import (
    # Authentication views
    RegisterView, LoginView, ForgotPasswordView, ResetPasswordView,

    # User management views
    UserViewSet, RoleViewSet,

    # Product management views
    CategoryViewSet, ProductViewSet, ProductImageViewSet, ProductModerationLogViewSet,

    # Shopping views
    CartViewSet, OrderViewSet, AdminOrderViewSet, ReviewViewSet,
    OfferViewSet,

    # Analytics views
    UserBehaviorView,

    # Page views
    HomePageView, SearchView,

    # Checkout process views
    ShippingOptionsView, PaymentProcessingView, PromoCodeView, StripeWebhookView,

    # Inventory management views
    InventoryManagementViewSet
)

router = DefaultRouter()

# User management endpoints
router.register(r'users', UserViewSet)
router.register(r'roles', RoleViewSet)

# Product management endpoints
router.register(r'categories', CategoryViewSet)
router.register(r'products', ProductViewSet)
router.register(r'product-images', ProductImageViewSet)
router.register(r'moderation', ProductModerationLogViewSet)

# Shopping endpoints
router.register(r'carts', CartViewSet, basename='cart')
router.register(r'orders', OrderViewSet, basename='order')
router.register(r'reviews', ReviewViewSet, basename='review')
router.register(r'offers', OfferViewSet)

# Admin endpoints
router.register(r'admin/orders', AdminOrderViewSet)

# Inventory management endpoints
router.register(r'inventory', InventoryManagementViewSet, basename='inventory')

urlpatterns = [
    path('', include(router.urls)),

    # Authentication endpoints
    path('auth/register/', RegisterView.as_view(), name='register'),
    path('auth/login/', LoginView.as_view(), name='login'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/forgot-password/', ForgotPasswordView.as_view(), name='forgot_password'),
    path('auth/reset-password/', ResetPasswordView.as_view(), name='reset_password'),

    # User behavior tracking
    path('track/', UserBehaviorView.as_view(), name='track_behavior'),

    # Home page data
    path('home/', HomePageView.as_view(), name='home_page'),

    # Search
    path('search/', SearchView.as_view(), name='search'),

    # Checkout related endpoints
    path('shipping_options/', ShippingOptionsView.as_view(), name='shipping_options'),
    path('payment/', PaymentProcessingView.as_view(), name='payment_processing'),
    path('apply_promo/', PromoCodeView.as_view(), name='apply_promo'),
    path('webhook/stripe/', StripeWebhookView.as_view(), name='stripe_webhook'),
]
