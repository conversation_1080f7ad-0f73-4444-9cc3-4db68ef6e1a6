# Generated by Django 4.2.7 on 2025-05-14 13:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0004_userbehavior_ip_address_userbehavior_page_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='InventoryChange',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity_change', models.IntegerField()),
                ('change_type', models.CharField(choices=[('add', 'Addition'), ('subtract', 'Subtraction'), ('order', 'Order Placement'), ('return', 'Order Return'), ('adjustment', 'Manual Adjustment'), ('initial', 'Initial Stock')], max_length=20)),
                ('previous_quantity', models.IntegerField()),
                ('new_quantity', models.IntegerField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_changes', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_changes', to='api.product')),
                ('reference_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventory_changes', to='api.order')),
            ],
        ),
    ]
