# Generated by Django 4.2.7 on 2025-05-14 13:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0003_productimage_productimage_unique_product_image_order'),
    ]

    operations = [
        migrations.AddField(
            model_name='userbehavior',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userbehavior',
            name='page',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='userbehavior',
            name='referrer',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userbehavior',
            name='session_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='userbehavior',
            name='user_agent',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='userbehavior',
            name='action',
            field=models.CharField(choices=[('search', 'Search'), ('view', 'View'), ('click', 'Click'), ('add_to_cart', 'Add to Cart'), ('purchase', 'Purchase'), ('page_view', 'Page View'), ('login', 'Login'), ('logout', 'Logout'), ('register', 'Register'), ('wishlist_add', 'Add to Wishlist'), ('review', 'Write Review'), ('share', 'Share Product')], max_length=20),
        ),
        migrations.AlterField(
            model_name='userbehavior',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_behaviors', to='api.product'),
        ),
    ]
