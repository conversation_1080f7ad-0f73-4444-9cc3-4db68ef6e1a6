#!/bin/bash

# Activate the virtual environment
echo "Activating virtual environment..."
# Check if pipenv is installed - try multiple methods
if command -v pipenv &> /dev/null; then
    echo "Found pipenv using command -v"
elif pip list | grep -q pipenv; then
    echo "Found pipenv in pip list"
elif pip3 list | grep -q pipenv; then
    echo "Found pipenv in pip3 list"
else
    echo "pipenv is not installed. Please install it with 'pip install pipenv'"
    exit 1
fi

# Run commands through pipenv run
echo "Running migrations..."
pipenv run python manage.py makemigrations
pipenv run python manage.py migrate

# Create superuser and sample data if setup.py exists
if [ -f "setup.py" ]; then
    echo "Creating sample data..."
    pipenv run python setup.py
else
    echo "setup.py not found, skipping sample data creation"
fi

# Run the development server
echo "Starting development server..."
pipenv run python manage.py runserver 0.0.0.0:8000
