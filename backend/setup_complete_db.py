#!/usr/bin/env python
import os
import sys
import django
import random
from datetime import timedelta
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce_backend.settings')
django.setup()

# Import models
from django.contrib.auth import get_user_model
from api.models import (
    User, Role, UserRole, Category, Product, ProductImage, 
    Offer, Cart, CartItem, Order, OrderLine, Review
)

def create_superuser():
    """Create a superuser for admin access"""
    User = get_user_model()
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_active=True,
            is_staff=True
        )
        print("Superuser created successfully!")
    else:
        print("Superuser already exists.")

def create_roles():
    """Create user roles"""
    roles = [
        {'name': 'Admin', 'description': 'Administrator with full access'},
        {'name': 'Customer', 'description': 'Regular customer'},
        {'name': 'Moderator', 'description': 'Content moderator with limited admin access'}
    ]
    
    for role_data in roles:
        Role.objects.get_or_create(
            name=role_data['name'],
            defaults={'description': role_data['description']}
        )
    print("Roles created successfully!")

def create_users():
    """Create regular users and assign roles"""
    # Create regular users
    for i in range(1, 11):
        user, created = User.objects.get_or_create(
            username=f'user{i}',
            defaults={
                'email': f'user{i}@example.com',
                'first_name': f'User {i}',
                'last_name': f'Lastname {i}'
            }
        )
        
        if created:
            user.set_password('password123')
            user.save()
    
    # Assign roles
    admin_role = Role.objects.get(name='Admin')
    customer_role = Role.objects.get(name='Customer')
    moderator_role = Role.objects.get(name='Moderator')
    
    # Assign admin role to admin user
    admin_user = User.objects.get(username='admin')
    UserRole.objects.get_or_create(user=admin_user, role=admin_role)
    
    # Assign customer role to all users
    for user in User.objects.all():
        UserRole.objects.get_or_create(user=user, role=customer_role)
    
    # Assign moderator role to a couple of users
    for user in User.objects.filter(username__in=['user1', 'user2']):
        UserRole.objects.get_or_create(user=user, role=moderator_role)
    
    print("Users created and roles assigned successfully!")

def create_categories():
    """Create main categories and subcategories"""
    # Main categories
    categories = [
        {'name': 'Electronics', 'description': 'Electronic devices and accessories'},
        {'name': 'Clothing', 'description': 'Apparel and fashion items'},
        {'name': 'Home & Kitchen', 'description': 'Home appliances and kitchen essentials'},
        {'name': 'Books', 'description': 'Books, e-books, and audiobooks'},
        {'name': 'Sports & Outdoors', 'description': 'Sports equipment and outdoor gear'}
    ]
    
    for category_data in categories:
        Category.objects.get_or_create(
            name=category_data['name'],
            defaults={
                'description': category_data['description'],
                'tags': [category_data['name'].lower()]
            }
        )
    
    # Subcategories
    subcategories = [
        {'name': 'Smartphones', 'parent_category': 'Electronics', 'description': 'Mobile phones and accessories'},
        {'name': 'Laptops', 'parent_category': 'Electronics', 'description': 'Laptops and accessories'},
        {'name': 'Audio', 'parent_category': 'Electronics', 'description': 'Headphones, speakers, and audio equipment'},
        {'name': 'Men\'s Clothing', 'parent_category': 'Clothing', 'description': 'Clothing for men'},
        {'name': 'Women\'s Clothing', 'parent_category': 'Clothing', 'description': 'Clothing for women'},
        {'name': 'Accessories', 'parent_category': 'Clothing', 'description': 'Fashion accessories'},
        {'name': 'Kitchen Appliances', 'parent_category': 'Home & Kitchen', 'description': 'Appliances for the kitchen'},
        {'name': 'Furniture', 'parent_category': 'Home & Kitchen', 'description': 'Home furniture'},
        {'name': 'Fiction', 'parent_category': 'Books', 'description': 'Fiction books'},
        {'name': 'Non-Fiction', 'parent_category': 'Books', 'description': 'Non-fiction books'},
        {'name': 'Fitness Equipment', 'parent_category': 'Sports & Outdoors', 'description': 'Equipment for fitness and exercise'},
        {'name': 'Outdoor Gear', 'parent_category': 'Sports & Outdoors', 'description': 'Gear for outdoor activities'}
    ]
    
    for subcategory_data in subcategories:
        parent_name = subcategory_data.pop('parent_category')
        parent = Category.objects.get(name=parent_name)
        
        Category.objects.get_or_create(
            name=subcategory_data['name'],
            defaults={**subcategory_data, 'parent_category': parent}
        )
    
    print("Categories and subcategories created successfully!")

def create_products():
    """Create products for each subcategory"""
    # Get all subcategories
    subcategories = Category.objects.filter(parent_category__isnull=False)
    
    # Product data by subcategory
    products_by_subcategory = {
        'Smartphones': [
            {'title': 'Premium Smartphone X', 'price': 999.99, 'stock_quantity': 50},
            {'title': 'Budget Smartphone Y', 'price': 299.99, 'stock_quantity': 100},
            {'title': 'Smartphone Case', 'price': 19.99, 'stock_quantity': 200},
            {'title': 'Screen Protector', 'price': 9.99, 'stock_quantity': 300}
        ],
        'Laptops': [
            {'title': 'Gaming Laptop Pro', 'price': 1499.99, 'stock_quantity': 30},
            {'title': 'Ultrabook Slim', 'price': 899.99, 'stock_quantity': 40},
            {'title': 'Laptop Backpack', 'price': 49.99, 'stock_quantity': 100},
            {'title': 'Laptop Cooling Pad', 'price': 29.99, 'stock_quantity': 80}
        ],
        'Audio': [
            {'title': 'Wireless Headphones', 'price': 199.99, 'stock_quantity': 60},
            {'title': 'Bluetooth Speaker', 'price': 79.99, 'stock_quantity': 70},
            {'title': 'Earbuds Pro', 'price': 129.99, 'stock_quantity': 90},
            {'title': 'Home Theater System', 'price': 399.99, 'stock_quantity': 25}
        ],
        'Men\'s Clothing': [
            {'title': 'Men\'s T-Shirt', 'price': 24.99, 'stock_quantity': 150},
            {'title': 'Men\'s Jeans', 'price': 49.99, 'stock_quantity': 120},
            {'title': 'Men\'s Jacket', 'price': 89.99, 'stock_quantity': 80},
            {'title': 'Men\'s Sweater', 'price': 59.99, 'stock_quantity': 100}
        ],
        'Women\'s Clothing': [
            {'title': 'Women\'s Dress', 'price': 69.99, 'stock_quantity': 130},
            {'title': 'Women\'s Blouse', 'price': 39.99, 'stock_quantity': 140},
            {'title': 'Women\'s Jeans', 'price': 54.99, 'stock_quantity': 110},
            {'title': 'Women\'s Jacket', 'price': 99.99, 'stock_quantity': 75}
        ],
        'Accessories': [
            {'title': 'Leather Belt', 'price': 29.99, 'stock_quantity': 180},
            {'title': 'Sunglasses', 'price': 49.99, 'stock_quantity': 160},
            {'title': 'Watch', 'price': 129.99, 'stock_quantity': 70},
            {'title': 'Scarf', 'price': 19.99, 'stock_quantity': 200}
        ],
        'Kitchen Appliances': [
            {'title': 'Coffee Maker', 'price': 79.99, 'stock_quantity': 60},
            {'title': 'Blender', 'price': 49.99, 'stock_quantity': 70},
            {'title': 'Toaster', 'price': 34.99, 'stock_quantity': 80},
            {'title': 'Microwave Oven', 'price': 149.99, 'stock_quantity': 40}
        ],
        'Furniture': [
            {'title': 'Sofa', 'price': 599.99, 'stock_quantity': 20},
            {'title': 'Coffee Table', 'price': 199.99, 'stock_quantity': 30},
            {'title': 'Bookshelf', 'price': 149.99, 'stock_quantity': 35},
            {'title': 'Desk', 'price': 249.99, 'stock_quantity': 25}
        ],
        'Fiction': [
            {'title': 'Mystery Novel', 'price': 14.99, 'stock_quantity': 200},
            {'title': 'Science Fiction Book', 'price': 12.99, 'stock_quantity': 180},
            {'title': 'Fantasy Series', 'price': 39.99, 'stock_quantity': 150},
            {'title': 'Romance Novel', 'price': 9.99, 'stock_quantity': 220}
        ],
        'Non-Fiction': [
            {'title': 'Self-Help Book', 'price': 19.99, 'stock_quantity': 170},
            {'title': 'Cookbook', 'price': 24.99, 'stock_quantity': 160},
            {'title': 'Biography', 'price': 17.99, 'stock_quantity': 140},
            {'title': 'History Book', 'price': 22.99, 'stock_quantity': 130}
        ],
        'Fitness Equipment': [
            {'title': 'Yoga Mat', 'price': 29.99, 'stock_quantity': 120},
            {'title': 'Dumbbells Set', 'price': 79.99, 'stock_quantity': 80},
            {'title': 'Exercise Bike', 'price': 299.99, 'stock_quantity': 30},
            {'title': 'Resistance Bands', 'price': 19.99, 'stock_quantity': 150}
        ],
        'Outdoor Gear': [
            {'title': 'Camping Tent', 'price': 149.99, 'stock_quantity': 40},
            {'title': 'Hiking Backpack', 'price': 89.99, 'stock_quantity': 60},
            {'title': 'Sleeping Bag', 'price': 69.99, 'stock_quantity': 70},
            {'title': 'Portable Grill', 'price': 59.99, 'stock_quantity': 50}
        ]
    }
    
    # Create products for each subcategory
    for subcategory in subcategories:
        if subcategory.name in products_by_subcategory:
            for product_data in products_by_subcategory[subcategory.name]:
                summary = f"A great {product_data['title']} for all your needs"
                description = f"This {product_data['title']} is a high-quality product that will meet all your expectations. It comes with a warranty and excellent customer support."
                
                Product.objects.get_or_create(
                    title=product_data['title'],
                    defaults={
                        **product_data,
                        'category': subcategory,
                        'summary': summary,
                        'description': description,
                        'tags': [subcategory.name.lower(), 'featured'] if random.random() > 0.7 else [subcategory.name.lower()],
                        'is_active': True
                    }
                )
    
    print("Products created successfully!")

def create_product_images():
    """Create dummy images for products"""
    # Get all products
    products = Product.objects.all()
    
    # Create media directory if it doesn't exist
    media_root = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'media')
    products_dir = os.path.join(media_root, 'products')
    os.makedirs(products_dir, exist_ok=True)
    
    for product in products:
        # Add main product image
        image_name = f"{product.slug}_main.jpg"
        dummy_image = SimpleUploadedFile(
            name=image_name,
            content=b'dummy image content',
            content_type='image/jpeg'
        )
        product.picture.save(image_name, dummy_image, save=True)
        
        # Add additional images (1-4 additional images)
        num_additional_images = random.randint(1, 4)
        
        for i in range(num_additional_images):
            image_name = f"{product.slug}_additional_{i + 1}.jpg"
            dummy_image = SimpleUploadedFile(
                name=image_name,
                content=b'dummy image content',
                content_type='image/jpeg'
            )
            
            product_image = ProductImage(
                product=product,
                order=i,
                alt_text=f"Additional image {i + 1} for {product.title}"
            )
            product_image.image.save(image_name, dummy_image, save=True)
    
    print("Product images created successfully!")

def create_offers():
    """Create special offers for products"""
    products = Product.objects.filter(is_active=True)
    
    # Create offers for random products
    for _ in range(10):
        product = random.choice(products)
        offer_type = random.choice(['percentage', 'flat'])
        discount_value = random.randint(5, 30) if offer_type == 'percentage' else random.randint(5, 50)
        
        start_date = timezone.now() - timedelta(days=random.randint(0, 10))
        end_date = start_date + timedelta(days=random.randint(5, 30))
        
        Offer.objects.create(
            product=product,
            name=f"{discount_value}% Off" if offer_type == 'percentage' else f"${discount_value} Off",
            offer_type=offer_type,
            discount_value=discount_value,
            start_date=start_date,
            end_date=end_date,
            is_festival=random.random() > 0.8,
            is_active=True
        )
    
    print("Offers created successfully!")

def create_carts_and_orders():
    """Create shopping carts and orders for users"""
    users = User.objects.filter(is_staff=False)
    products = Product.objects.filter(is_active=True)
    
    for user in users:
        # Create active cart
        cart, _ = Cart.objects.get_or_create(
            created_by=user,
            status='active',
            defaults={'created_at': timezone.now()}
        )
        
        # Add random items to cart
        for _ in range(random.randint(1, 5)):
            product = random.choice(products)
            quantity = random.randint(1, 3)
            
            CartItem.objects.get_or_create(
                cart=cart,
                product=product,
                defaults={
                    'price': product.price,
                    'quantity': quantity
                }
            )
        
        # Create completed orders
        for _ in range(random.randint(0, 3)):
            # Create order
            order_products = random.sample(list(products), random.randint(1, 5))
            total_price = sum(product.price * random.randint(1, 3) for product in order_products)
            
            order = Order.objects.create(
                user=user,
                total_price=total_price,
                payment_status=random.choice(['pending', 'paid']),
                delivery_status=random.choice(['processing', 'shipped', 'delivered']),
                created_at=timezone.now() - timedelta(days=random.randint(1, 30))
            )
            
            # Create order lines
            for product in order_products:
                quantity = random.randint(1, 3)
                OrderLine.objects.create(
                    order=order,
                    product=product,
                    price=product.price,
                    quantity=quantity
                )
    
    print("Carts and orders created successfully!")

def create_reviews():
    """Create product reviews"""
    users = User.objects.filter(is_staff=False)
    products = Product.objects.filter(is_active=True)
    
    for product in products:
        # Add 0-5 reviews per product
        for _ in range(random.randint(0, 5)):
            user = random.choice(users)
            rating = random.uniform(3.0, 5.0)  # Mostly positive reviews
            
            # Skip if review already exists
            if Review.objects.filter(user=user, product=product).exists():
                continue
            
            Review.objects.create(
                user=user,
                product=product,
                rating=rating,
                comment=f"This is a {'great' if rating > 4 else 'good'} product. {'Highly recommended!' if rating > 4.5 else 'Recommended.'}"
            )
    
    print("Reviews created successfully!")

def main():
    """Main function to run all setup steps"""
    print("Starting database setup...")
    
    create_superuser()
    create_roles()
    create_users()
    create_categories()
    create_products()
    create_product_images()
    create_offers()
    create_carts_and_orders()
    create_reviews()
    
    print("Database setup completed successfully!")

if __name__ == '__main__':
    main()
